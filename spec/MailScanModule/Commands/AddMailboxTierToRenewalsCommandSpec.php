<?php

declare(strict_types=1);

namespace spec\MailScanModule\Commands;

use Doctrine\Common\Collections\ArrayCollection;
use Entities\Company;
use Entities\Customer;
use Entities\Service;
use Exceptions\Technical\NodeException;
use MailScanModule\Commands\AddMailboxTierToRenewalsCommand;
use MailScanModule\Emailers\MailboxEmailer;
use Models\Products\BasketProduct;
use Models\Products\Product;
use PhpSpec\ObjectBehavior;
use Prophecy\Argument;
use Psr\Log\LoggerInterface;
use Services\NodeService;
use Services\ServiceService;
use ServicesAdminModule\Facades\AddServiceFacade;
use ServicesAdminModule\Forms\AddServiceForm;
use ServicesAdminModule\Forms\AddServiceFormData;

class AddMailboxTierToRenewalsCommandSpec extends ObjectBehavior
{
    public function let(
        LoggerInterface $logger,
        NodeService $nodeService,
        ServiceService $serviceService,
        AddServiceFacade $addServiceFacade,
        MailboxEmailer $mailboxEmailer,
    ): void {
        $this->beConstructedWith(
            $logger,
            $nodeService,
            $serviceService,
            $addServiceFacade,
            $mailboxEmailer
        );
    }

    public function it_is_initializable(): void
    {
        $this->shouldHaveType(AddMailboxTierToRenewalsCommand::class);
    }

    public function it_logs_and_returns_when_no_services_found(
        LoggerInterface $logger,
        NodeService $nodeService,
        ServiceService $serviceService,
        BasketProduct $packageProduct,
        BasketProduct $mailboxProduct,
    ): void {
        $packageList = \sprintf('%s', Product::PRODUCT_RENEWAL_REGISTERED_OFFICE);
        $defaultMailbox = Product::PRODUCT_MAILBOX_STANDARD_PRIVACY_INITIAL_1_MONTH;

        $nodeService->requiredProductByName(Product::PRODUCT_RENEWAL_REGISTERED_OFFICE)->willReturn($packageProduct);
        $packageProduct->getNodeName()->willReturn(Product::PRODUCT_RENEWAL_REGISTERED_OFFICE);
        $packageProduct->getId()->willReturn(Product::PRODUCT_RENEWAL_REGISTERED_OFFICE);
        $packageProduct->getName()->willReturn('Renewal Registered Office');

        // Stub mailbox product resolution
        $nodeService->requiredProductByName($defaultMailbox)->willReturn($mailboxProduct);
        $mailboxProduct->getNodeName()->willReturn(Product::PRODUCT_MAILBOX_STANDARD_PRIVACY_INITIAL_1_MONTH);
        $mailboxProduct->getId()->willReturn(456);
        $mailboxProduct->getName()->willReturn('Mailbox Standard 1 Month');

        $serviceService->getMailboxFreeTrialEligibleServices(
            [Product::PRODUCT_RENEWAL_REGISTERED_OFFICE],
            true,
            false,
            null,
            null
        )->willReturn([]);

        $logger->info(Argument::cetera())->shouldBeCalled();

        $logger->info('No services found')->shouldBeCalled();

        $this->execute($packageList);
    }

    public function it_throws_exception_for_invalid_package(
        NodeService $nodeService,
        BasketProduct $validProduct,
        BasketProduct $invalidProduct,
    ): void {
        $packageList = \sprintf('%s,%s', Product::PRODUCT_RENEWAL_REGISTERED_OFFICE, Product::PRODUCT_REGISTERED_OFFICE_SERVICE_ADDRESS_BUNDLE);

        $nodeService->requiredProductByName(Product::PRODUCT_RENEWAL_REGISTERED_OFFICE)->willReturn($validProduct);
        $validProduct->getNodeName()->willReturn(Product::PRODUCT_RENEWAL_REGISTERED_OFFICE);
        $validProduct->getId()->willReturn(Product::PRODUCT_RENEWAL_REGISTERED_OFFICE);
        $validProduct->getName()->willReturn('Renewal Registered Office');

        $nodeService->requiredProductByName(Product::PRODUCT_REGISTERED_OFFICE_SERVICE_ADDRESS_BUNDLE)->willReturn($invalidProduct);
        $invalidProduct->getNodeName()->willReturn(Product::PRODUCT_REGISTERED_OFFICE_SERVICE_ADDRESS_BUNDLE);
        $invalidProduct->getId()->willReturn(Product::PRODUCT_REGISTERED_OFFICE_SERVICE_ADDRESS_BUNDLE);
        $invalidProduct->getName()->willReturn('Registered Office Service Address Bundle');

        $this->shouldThrow(NodeException::class);

        $this->execute($packageList);
    }

    public function it_throws_exception_for_custom_method_without_dates(): void
    {
        $packageList = '123,321';

        $this->shouldThrow(\InvalidArgumentException::class);

        $this->execute($packageList);
    }

    public function it_throws_exception_for_custom_method_with_invalid_date_format(): void
    {
        $packageList = '123,321';
        $badInitial = 'not-a-date';
        $badEnd = 'also-not-a-date';

        $this->shouldThrow(\InvalidArgumentException::class);

        $this->execute(
            packageList: $packageList,
            methodType: AddMailboxTierToRenewalsCommand::METHOD_TYPE_CUSTOM,
            startDate: $badInitial,
            endDate: $badEnd
        );
    }

    public function it_adds_mailbox_to_specific_company_id_and_does_not_send_email(
        LoggerInterface $logger,
        NodeService $nodeService,
        ServiceService $serviceService,
        AddServiceFacade $addServiceFacade,
        MailboxEmailer $mailboxEmailer,
        BasketProduct $packageProduct,
        BasketProduct $mailboxProduct,
        Service $service,
        Company $company,
        Customer $customer,
    ): void {
        $packageList = sprintf('%s', Product::PRODUCT_RENEWAL_REGISTERED_OFFICE);
        $defaultMailbox = Product::PRODUCT_MAILBOX_STANDARD_PRIVACY_INITIAL_1_MONTH;
        $companyId = 123;

        $nodeService->requiredProductByName(Product::PRODUCT_RENEWAL_REGISTERED_OFFICE)->willReturn($packageProduct);
        $packageProduct->getNodeName()->willReturn(Product::PRODUCT_RENEWAL_REGISTERED_OFFICE);
        $packageProduct->getId()->willReturn(Product::PRODUCT_RENEWAL_REGISTERED_OFFICE);
        $packageProduct->getName()->willReturn('Renewal Registered Office');

        $nodeService->requiredProductByName($defaultMailbox)->willReturn($mailboxProduct);
        $mailboxProduct->getNodeName()->willReturn($defaultMailbox);
        $mailboxProduct->getId()->willReturn(456);
        $mailboxProduct->getName()->willReturn('Mailbox Standard 1 Month');
        $mailboxProduct->getServiceTypeId()->willReturn(Service::TYPE_MAILBOX_STANDARD);
        $mailboxProduct->getRenewalProduct()->willReturn($mailboxProduct);

        $service->getId()->willReturn(789);
        $service->getCompany()->willReturn($company);
        $service->getCustomer()->willReturn($customer);
        $service->getServiceName()->willReturn('Test Service');

        $company->getId()->willReturn($companyId);
        $company->getActiveOrLatestMailboxService()->willReturn(null);

        $customer->getId()->willReturn(111);
        $customer->getEmail()->willReturn('<EMAIL>');

        $serviceService->getMailboxFreeTrialEligibleServices(
            [Product::PRODUCT_RENEWAL_REGISTERED_OFFICE],
            true,
            false,
            null,
            null
        )->willReturn(new ArrayCollection([$service->getWrappedObject()]));

        $today = new \DateTime();

        $addServiceFacade->addService(
            (new AddServiceFormData())
                ->setProductName($defaultMailbox)
                ->setDuration('+1 month')
                ->setStartDate($today->format('Y-m-d'))
                ->setPaymentDate($today)
                ->setReason(AddServiceForm::REASON_COMMAND)
                ->setReference('AddMailboxTierToRenewalsCommand')
                ->setTotalAmount(0),
            $company
        )->shouldBeCalled();

        $mailboxEmailer->sendFreeTrialEmail(Argument::cetera())->shouldNotBeCalled();

        $logger->info(Argument::type('string'))->shouldBeCalledTimes(4);

        $this->execute(
            packageList: $packageList,
            sendEmail: false,
            companyId: $companyId,
            dryRun: false
        );
    }

    public function it_adds_mailbox_to_all_eligible_companies_and_sends_email(
        LoggerInterface $logger,
        NodeService $nodeService,
        ServiceService $serviceService,
        AddServiceFacade $addServiceFacade,
        MailboxEmailer $mailboxEmailer,
        BasketProduct $packageProduct,
        BasketProduct $mailboxProduct,
        BasketProduct $registeredOfficeProduct,
        Service $service1,
        Service $service2,
        Company $company1,
        Company $company2,
        Customer $customer1,
        Customer $customer2,
    ): void {
        $packageList = sprintf('%s', Product::PRODUCT_RENEWAL_REGISTERED_OFFICE);
        $defaultMailbox = Product::PRODUCT_MAILBOX_STANDARD_PRIVACY_INITIAL_1_MONTH;

        $nodeService->requiredProductByName(Product::PRODUCT_RENEWAL_REGISTERED_OFFICE)->willReturn($packageProduct);
        $packageProduct->getNodeName()->willReturn(Product::PRODUCT_RENEWAL_REGISTERED_OFFICE);
        $packageProduct->getId()->willReturn(Product::PRODUCT_RENEWAL_REGISTERED_OFFICE);
        $packageProduct->getName()->willReturn('Renewal Registered Office');

        $nodeService->requiredProductByName($defaultMailbox)->willReturn($mailboxProduct);
        $mailboxProduct->getNodeName()->willReturn($defaultMailbox);
        $mailboxProduct->getId()->willReturn(456);
        $mailboxProduct->getName()->willReturn('Mailbox Standard 1 Month');
        $mailboxProduct->getServiceTypeId()->willReturn(Service::TYPE_MAILBOX_STANDARD);
        $mailboxProduct->getRenewalProduct()->willReturn($mailboxProduct);
        $mailboxProduct->getPrice(Argument::any())->willReturn(99.99);

        $nodeService->requiredProductByName((string) Product::PRODUCT_REGISTERED_OFFICE)->willReturn($registeredOfficeProduct);
        $registeredOfficeProduct->getMailboxPayToReleaseFeePostItem()->willReturn(5.00);

        $service1->getId()->willReturn(789);
        $service1->getCompany()->willReturn($company1);
        $service1->getCustomer()->willReturn($customer1);
        $service1->getServiceName()->willReturn('Test Service 1');

        $service2->getId()->willReturn(790);
        $service2->getCompany()->willReturn($company2);
        $service2->getCustomer()->willReturn($customer2);
        $service2->getServiceName()->willReturn('Test Service 2');

        $company1->getActiveOrLatestMailboxService()->willReturn(null);
        $company1->getCompanyName()->willReturn('Company 1');
        $company2->getActiveOrLatestMailboxService()->willReturn(null);
        $company2->getCompanyName()->willReturn('Company 2');

        $customer1->getId()->willReturn(111);
        $customer1->getEmail()->willReturn('<EMAIL>');
        $customer1->getFirstName()->willReturn('John');

        $customer2->getId()->willReturn(222);
        $customer2->getEmail()->willReturn('<EMAIL>');
        $customer2->getFirstName()->willReturn('Jane');

        $serviceService->getMailboxFreeTrialEligibleServices(
            [Product::PRODUCT_RENEWAL_REGISTERED_OFFICE],
            true,
            false,
            null,
            null
        )->willReturn([$service1, $service2]);

        $addServiceFacade->addService(
            Argument::type(AddServiceFormData::class),
            Argument::any()
        )->shouldBeCalledTimes(2);

        $mailboxEmailer->sendFreeTrialEmail(
            Argument::any(),
            Argument::type('array')
        )->shouldBeCalledTimes(2);

        $nodeService->requiredProductByName(Argument::any())->willReturn($mailboxProduct);

        $logger->info(Argument::type('string'))->shouldBeCalledTimes(6);

        $this->execute(
            packageList: $packageList,
            dryRun: false
        );
    }
}
