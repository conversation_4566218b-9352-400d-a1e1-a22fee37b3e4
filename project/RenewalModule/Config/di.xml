<?xml version="1.0" ?>
<container xmlns="http://symfony.com/schema/dic/services"
           xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>
        <defaults public="true" />
        <service class="RenewalModule\Providers\BenefitsAndConsequencesProviderFactory" id="renewal_module.providers.benefits_and_consequences_provider_factory">
            <argument id="content_module.yaml_parser" type="service"/>
        </service>

        <service class="RenewalModule\Providers\BenefitsAndConsequencesProvider" id="renewal_module.providers.benefits_and_consequences_provider">
            <factory service="renewal_module.providers.benefits_and_consequences_provider_factory" method="create"/>
            <argument type="service">
                <service class="Utils\File">
                    <factory class="Utils\File" method="fromExisting" />
                    <argument type="service">
                        <service class="Utils\Directory">
                            <factory class="Utils\Directory" method="fromPath" />
                            <argument>%email_variables.files.services_renewal.directory%</argument>
                        </service>
                    </argument>
                    <argument>%email_variables.files.services_renewal.filename%</argument>
                </service>
            </argument>
        </service>

        <service class="RenewalModule\Views\AutoRenewalEmailViewFactory" id="renewal_module.views.auto_renewal_email_view_factory">
            <argument id="url_generator" type="service"/>
            <argument id="renewal_module.providers.benefits_and_consequences_provider" type="service"/>
            <argument type="string">%update_my_card_detail_link%</argument>
            <argument type="string">%how_to_change_registered_address_link%</argument>
        </service>

    </services>
</container>

