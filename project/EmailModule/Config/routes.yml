admin_email_templates:
  path: /admin/email-templates/placeholders/
  defaults:
    feature: role_admin
    controller: email_module.controllers.admin.email_template_controller
    action: listEmails

admin_email_template_placeholder:
  path: /admin/email-templates/placeholders/{placeholderName}/
  defaults:
    feature: role_admin
    controller: email_module.controllers.admin.email_template_controller
    action: showEmailByPlaceholder

admin_emailtest:
  path: /admin/emailtest/
  defaults:
    controller: email_module.controllers.admin.email_test_controller
    action: list

admin_emailtest_preview:
  path: /admin/emailtest_preview/
  defaults:
    controller: email_module.controllers.admin.email_test_controller
    action: preview
  requirements:
    templateFile: ".+"

admin_emailtest_preview_iframe:
  path: /admin/emailtest_preview_iframe/
  defaults:
    controller: email_module.controllers.admin.email_test_controller
    action: iframe
  requirements:
    templateFile: ".+"