<?php

declare(strict_types=1);

namespace MailScanModule\Services;

use CompanyModule\Entities\Settings\PostItemHandlingSetting;
use Entities\Company;
use MailScanModule\Deciders\MailboxTierDecider;
use MailScanModule\Helpers\MailboxProductPropertyHelper;

readonly class MailboxService
{
    public function __construct(
        private MailboxTierDecider $mailboxTierDecider,
    ) {
    }

    /**
     * @throws \Exception
     */
    public function getMailBoxTierFromCompany(Company $company): ?int
    {
        $mailboxService = $company->getActiveMailboxService();

        if (empty($mailboxService)) {
            return null;
        }

        return $this->mailboxTierDecider->determineMailboxTier($mailboxService);
    }

    public function getMailboxInboxSettingsConfig(): array
    {
        return [
            MailboxTierDecider::TIER_1 => [
                MailboxProductPropertyHelper::FORMAT_POST_ITEM => [
                    PostItemHandlingSetting::VALUE_ITEM_SCAN_ONLY
                ],
            ],
            MailboxTierDecider::TIER_2 => [
                MailboxProductPropertyHelper::FORMAT_POST_ITEM => [
                    PostItemHandlingSetting::VALUE_ITEM_SCAN_ONLY
                ],
                MailboxProductPropertyHelper::FORMAT_PARCEL => [
                    PostItemHandlingSetting::VALUE_PARCEL_REJECT,
                    PostItemHandlingSetting::VALUE_PARCEL_COLLECT
                ],
            ],
            MailboxTierDecider::TIER_3 => [
                MailboxProductPropertyHelper::FORMAT_POST_ITEM => [
                    PostItemHandlingSetting::VALUE_ITEM_SCAN_ONLY,
                    PostItemHandlingSetting::VALUE_ITEM_SCAN_AND_COLLECT,
                    PostItemHandlingSetting::VALUE_ITEM_SCAN_AND_POST
                ],
                MailboxProductPropertyHelper::FORMAT_PARCEL => [
                    PostItemHandlingSetting::VALUE_PARCEL_REJECT,
                    PostItemHandlingSetting::VALUE_PARCEL_COLLECT,
                    PostItemHandlingSetting::VALUE_PARCEL_POST
                ],
            ],
            MailboxTierDecider::TIER_4 => [
                MailboxProductPropertyHelper::FORMAT_POST_ITEM => [
                    PostItemHandlingSetting::VALUE_ITEM_SCAN_ONLY,
                    PostItemHandlingSetting::VALUE_ITEM_SCAN_AND_COLLECT,
                    PostItemHandlingSetting::VALUE_ITEM_SCAN_AND_POST
                ],
                MailboxProductPropertyHelper::FORMAT_PARCEL => [
                    PostItemHandlingSetting::VALUE_PARCEL_REJECT,
                    PostItemHandlingSetting::VALUE_PARCEL_COLLECT,
                    PostItemHandlingSetting::VALUE_PARCEL_POST
                ],
            ],
        ];
    }


    /**
     * @throws \Exception
     */
    public function isSettingAllowed(Company $company, string $format, int $settingValue): bool
    {
        return match ($format) {
            MailboxProductPropertyHelper::FORMAT_POST_ITEM => in_array($settingValue, $this->getMailboxInboxSettingsConfig()[$this->getMailBoxTierFromCompany($company)][MailboxProductPropertyHelper::FORMAT_POST_ITEM]),
            MailboxProductPropertyHelper::FORMAT_PARCEL => in_array($settingValue, $this->getMailboxInboxSettingsConfig()[$this->getMailBoxTierFromCompany($company)][MailboxProductPropertyHelper::FORMAT_PARCEL]),
            default => throw new \Exception('Unknown format'),
        };
    }
}
