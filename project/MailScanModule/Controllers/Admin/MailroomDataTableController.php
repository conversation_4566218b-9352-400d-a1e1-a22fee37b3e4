<?php

declare(strict_types=1);

namespace MailScanModule\Controllers\Admin;

use CompanyModule\Facades\MailForwardingFacade;
use Entities\Company;
use MailScanModule\ApiClient\MailroomApiClient;
use MailScanModule\Enums\StatusEnum;
use MailScanModule\Helpers\MailboxProductPropertyHelper;
use RouterModule\Domain\FlashMessage;
use RouterModule\Helpers\ControllerHelper;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;
use TemplateModule\Renderers\IRenderer;

class MailroomDataTableController
{
    private const PER_PAGE = 20;

    public function __construct(
        private IRenderer $renderer,
        private MailroomApiClient $mailroomApiClient,
        private ControllerHelper $controllerHelper,
        private MailForwardingFacade $mailForwardingFacade,
    ) {
    }

    /**
     * @throws \Exception
     */
    public function mailroomAdminCompanyTable(Company $company, int $pnum = 1): Response
    {
        $postItemsWithTotal = $this->mailroomApiClient->getPostItemsForCompany(
            $company->getCompanyNumber(),
            [
                'pnum' => $pnum,
                'psize' => self::PER_PAGE,
            ]
        );

        return $this->renderer->render(
            [
                'company' => $company,
                'postItems' => $postItemsWithTotal['postItems']->jsonSerialize(),
                'total' => $postItemsWithTotal['total'],
                'pnum' => $pnum,
                'psize' => self::PER_PAGE,
            ],
        );
    }

    /**
     * @throws \Exception
     */
    public function releaseScan(Company $company, string $postItemId, int $pnum = 1): RedirectResponse
    {
        try {

            // @TODO deduct quota

            $this->mailroomApiClient->setPostItemStatus(
                [$this->mailroomApiClient->getPostItemById($postItemId)],
                StatusEnum::STATUS_SCAN_ONLY->value
            );

            $this->controllerHelper->notify(
                FlashMessage::success(sprintf(
                    'Scan released successfully for post item %s',
                    $postItemId
                ))
            );
        } catch (\Exception $e) {
            $this->controllerHelper->notify(
                FlashMessage::error(sprintf(
                    'Failed to release scan for post item %s: %s',
                    $postItemId,
                    $e->getMessage()
                ))
            );

            return $this->controllerHelper->redirectionTo(
                '551 view',
                [
                    'company_id' => $company->getId(),
                ],
            );
        }

        return $this->controllerHelper->redirectionTo(
            'admin_mailroom_post_items',
            [
                'company' => $company->getId(),
                'pnum' => $pnum,
            ],
        );
    }
}
