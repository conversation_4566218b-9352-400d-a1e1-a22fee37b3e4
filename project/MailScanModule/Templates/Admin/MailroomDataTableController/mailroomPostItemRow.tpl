<tr>
    <td>{$item->getDtc()|datetime}</td>
    <td>{$item->getId()}</td>
    <td>{$item->getBatch()}</td>
    <td>{$item->getType()}</td>
    <td>{$item->getSender()}</td>
    <td>
        <div style="display: block;">
            <a 
                href="#" 
                onclick="openPdfInNewTab('{url route="company_inbox.api.download_item" company=$company->getId() item=$item->getId()}', '{$item->getCompanyName()}', '{$item->getDtc()|date_format:'%Y-%m-%d-%H-%i-%s'}'); return false;"
                class="download-link"
            >{$item->getFileName()}</a>
            <div id="spinner-{$item->getId()}" class="spinner-border" style="vertical-align: top; display: none;" role="status">
                <span class="sr-only">Loading...</span>
            </div>
        </div>
    </td>
    <td>{$item->getInboxStatus()}</td>
    <td>
        {if $item->isStatusWaitingPayment()}
            <a href="{url route="admin_mailroom_release_scan" company=$company->getId() postItemId=$item->getId() pnum=$pnum}"
               onclick="return confirm('Do you really want to release this post-item scan? This action is irreversible.');">
                Release Scan
            </a>
        {/if}
    </td>
</tr>


<script>
    async function openPdfInNewTab(url, companyName, dtc) {
        const spinnerId = 'spinner-{$item->getId()}';
        const spinner = document.getElementById(spinnerId);
        const newTab = window.open("", '_blank');
        newTab.document.title = "Loading...";

        try {
            spinner.style.display = 'inline-block';
            const response = await fetch(url);
            const data = await response.json();

            if (data.status === "error") {
                throw new Error("Error downloading Post Item. Please contact support.");
            }

            const byteCharacters = atob(data.encodedFile);
            const byteNumbers = new Array(byteCharacters.length);
            for (let i = 0; i < byteCharacters.length; i++) {
                byteNumbers[i] = byteCharacters.charCodeAt(i);
            }
            const byteArray = new Uint8Array(byteNumbers);
            const blob = new Blob([byteArray], { type: 'application/pdf' });
            const blobUrl = URL.createObjectURL(blob);

            newTab.location.href = blobUrl;
            newTab.document.title = '{$companyName}_{$item->getDtc()|date_format:"%Y-%m-%d-%H-%i-%s"}';
        } catch (error) {
            console.error("Error fetching post items:", error);
            newTab.document.write('<body style="font-family: sans-serif; text-align: center; margin: 20px auto; max-width: 600px"><h1>Error</h1><p>'+error.message+'</p></body>');
            newTab.document.title = "Error";
            newTab.document.close();
        } finally {
            spinner.style.display = 'none';
        }
    }
</script>