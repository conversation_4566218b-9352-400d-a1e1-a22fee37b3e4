<?xml version="1.0" ?>
<container xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
           xmlns="http://symfony.com/schema/dic/services"
           xsi:schemaLocation="http://symfony.com/schema/dic/services http://symfony.com/schema/dic/services/services-1.0.xsd">
    <services>
        <defaults public="true" />

        <service class="MailScanModule\UseCase\InboxSettings\Api\SaveCompanyInboxSettings\Command"
                 id="mail_scan_module.use_case.inbox_settings_controller.api.save_company_inbox_settings.command">
            <argument type="service" id="mail_scan_module.use_case.inbox_settings_controller.api.save_company_inbox_settings.factory"/>
            <argument type="service" id="mail_scan_module.facades.mail_forwarding_facade"/>
            <argument type="service" id="company_module.facades.mail_forwarding_facade"/>
            <argument type="service" id="company_module.facades.post_item_handling_facade"/>
            <argument type="service" id="mail_scan_module.providers.mailbox_product_provider"/>
            <argument type="service" id="mail_scan_module.services.mailbox_service"/>
        </service>

        <service class="MailScanModule\UseCase\InboxSettings\Api\SaveCompanyInboxSettings\Factory"
                 id="mail_scan_module.use_case.inbox_settings_controller.api.save_company_inbox_settings.factory">
        </service>

    </services>
</container>
