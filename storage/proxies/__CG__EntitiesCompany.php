<?php

namespace CMS\Proxy\__CG__\Entities;


/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class Company extends \Entities\Company implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }

    /**
     * {@inheritDoc}
     * @param string $name
     */
    public function & __get($name)
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__get', [$name]);
        return parent::__get($name);
    }

    /**
     * {@inheritDoc}
     * @param string $name
     * @param mixed  $value
     */
    public function __set($name, $value)
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__set', [$name, $value]);
        return parent::__set($name, $value);
    }

    /**
     * {@inheritDoc}
     * @param  string $name
     * @return boolean
     */
    public function __isset($name)
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__isset', [$name]);

        return parent::__isset($name);
    }

    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', '' . "\0" . 'Entities\\Company' . "\0" . 'companyId', '' . "\0" . 'Entities\\Company' . "\0" . 'productId', '' . "\0" . 'Entities\\Company' . "\0" . 'isCertificatePrinted', '' . "\0" . 'Entities\\Company' . "\0" . 'isBronzeCoverLetterPrinted', '' . "\0" . 'Entities\\Company' . "\0" . 'isMaPrinted', '' . "\0" . 'Entities\\Company' . "\0" . 'isMaCoverLetterPrinted', '' . "\0" . 'Entities\\Company' . "\0" . 'companyName', '' . "\0" . 'Entities\\Company' . "\0" . 'companyNumber', '' . "\0" . 'Entities\\Company' . "\0" . 'authenticationCode', '' . "\0" . 'Entities\\Company' . "\0" . 'incorporationDate', '' . "\0" . 'Entities\\Company' . "\0" . 'dissolutionDate', '' . "\0" . 'Entities\\Company' . "\0" . 'companyCategory', '' . "\0" . 'Entities\\Company' . "\0" . 'jurisdiction', '' . "\0" . 'Entities\\Company' . "\0" . 'madeUpDate', '' . "\0" . 'Entities\\Company' . "\0" . 'premise', '' . "\0" . 'Entities\\Company' . "\0" . 'street', '' . "\0" . 'Entities\\Company' . "\0" . 'thoroughfare', '' . "\0" . 'Entities\\Company' . "\0" . 'postTown', '' . "\0" . 'Entities\\Company' . "\0" . 'county', '' . "\0" . 'Entities\\Company' . "\0" . 'country', '' . "\0" . 'Entities\\Company' . "\0" . 'postcode', '' . "\0" . 'Entities\\Company' . "\0" . 'careOfName', '' . "\0" . 'Entities\\Company' . "\0" . 'poBox', '' . "\0" . 'Entities\\Company' . "\0" . 'sailPremise', '' . "\0" . 'Entities\\Company' . "\0" . 'sailStreet', '' . "\0" . 'Entities\\Company' . "\0" . 'sailThoroughfare', '' . "\0" . 'Entities\\Company' . "\0" . 'sailPostTown', '' . "\0" . 'Entities\\Company' . "\0" . 'sailCounty', '' . "\0" . 'Entities\\Company' . "\0" . 'sailCountry', '' . "\0" . 'Entities\\Company' . "\0" . 'sailPostcode', '' . "\0" . 'Entities\\Company' . "\0" . 'sailCareOfName', '' . "\0" . 'Entities\\Company' . "\0" . 'sailPoBox', '' . "\0" . 'Entities\\Company' . "\0" . 'sicCode1', '' . "\0" . 'Entities\\Company' . "\0" . 'sicCode2', '' . "\0" . 'Entities\\Company' . "\0" . 'sicCode3', '' . "\0" . 'Entities\\Company' . "\0" . 'sicCode4', '' . "\0" . 'Entities\\Company' . "\0" . 'sicDescription', '' . "\0" . 'Entities\\Company' . "\0" . 'companyStatus', '' . "\0" . 'Entities\\Company' . "\0" . 'countryOfOrigin', '' . "\0" . 'Entities\\Company' . "\0" . 'accountsRefDate', '' . "\0" . 'Entities\\Company' . "\0" . 'accountsOverdue', '' . "\0" . 'Entities\\Company' . "\0" . 'accountsNextPeriodStartDate', '' . "\0" . 'Entities\\Company' . "\0" . 'accountsNextPeriodEndDate', '' . "\0" . 'Entities\\Company' . "\0" . 'accountsNextDueDate', '' . "\0" . 'Entities\\Company' . "\0" . 'accountsLastType', '' . "\0" . 'Entities\\Company' . "\0" . 'accountsLastPeriodStartDate', '' . "\0" . 'Entities\\Company' . "\0" . 'accountsLastMadeUpDate', '' . "\0" . 'Entities\\Company' . "\0" . 'returnsNextMakeUpDate', '' . "\0" . 'Entities\\Company' . "\0" . 'returnsNextDueDate', '' . "\0" . 'Entities\\Company' . "\0" . 'returnsLastMadeUpDate', '' . "\0" . 'Entities\\Company' . "\0" . 'returnsOverdue', '' . "\0" . 'Entities\\Company' . "\0" . 'dcaId', '' . "\0" . 'Entities\\Company' . "\0" . 'registeredOfficeId', '' . "\0" . 'Entities\\Company' . "\0" . 'serviceAddressId', '' . "\0" . 'Entities\\Company' . "\0" . 'nomineeDirectorId', '' . "\0" . 'Entities\\Company' . "\0" . 'nomineeSecretaryId', '' . "\0" . 'Entities\\Company' . "\0" . 'nomineeSubscriberId', '' . "\0" . 'Entities\\Company' . "\0" . 'annualReturnId', '' . "\0" . 'Entities\\Company' . "\0" . 'changeNameId', '' . "\0" . 'Entities\\Company' . "\0" . 'ereminderId', '' . "\0" . 'Entities\\Company' . "\0" . 'documentDate', '' . "\0" . 'Entities\\Company' . "\0" . 'documentId', '' . "\0" . 'Entities\\Company' . "\0" . 'acceptedDate', '' . "\0" . 'Entities\\Company' . "\0" . 'cashBackAmount', '' . "\0" . 'Entities\\Company' . "\0" . 'noPscReason', '' . "\0" . 'Entities\\Company' . "\0" . 'registeredEmailAddress', '' . "\0" . 'Entities\\Company' . "\0" . 'locked', '' . "\0" . 'Entities\\Company' . "\0" . 'hidden', '' . "\0" . 'Entities\\Company' . "\0" . 'deleted', '' . "\0" . 'Entities\\Company' . "\0" . 'etag', '' . "\0" . 'Entities\\Company' . "\0" . 'dateLastSynced', '' . "\0" . 'Entities\\Company' . "\0" . 'dtc', '' . "\0" . 'Entities\\Company' . "\0" . 'dtm', '' . "\0" . 'Entities\\Company' . "\0" . 'services', '' . "\0" . 'Entities\\Company' . "\0" . 'customer', '' . "\0" . 'Entities\\Company' . "\0" . 'order', '' . "\0" . 'Entities\\Company' . "\0" . 'formSubmissions', '' . "\0" . 'Entities\\Company' . "\0" . 'registerMembers', '' . "\0" . 'Entities\\Company' . "\0" . 'toolkitOffers', '' . "\0" . 'Entities\\Company' . "\0" . 'settings', '' . "\0" . 'Entities\\Company' . "\0" . 'businessPhoneOption'];
        }

        return ['__isInitialized__', '' . "\0" . 'Entities\\Company' . "\0" . 'companyId', '' . "\0" . 'Entities\\Company' . "\0" . 'productId', '' . "\0" . 'Entities\\Company' . "\0" . 'isCertificatePrinted', '' . "\0" . 'Entities\\Company' . "\0" . 'isBronzeCoverLetterPrinted', '' . "\0" . 'Entities\\Company' . "\0" . 'isMaPrinted', '' . "\0" . 'Entities\\Company' . "\0" . 'isMaCoverLetterPrinted', '' . "\0" . 'Entities\\Company' . "\0" . 'companyName', '' . "\0" . 'Entities\\Company' . "\0" . 'companyNumber', '' . "\0" . 'Entities\\Company' . "\0" . 'authenticationCode', '' . "\0" . 'Entities\\Company' . "\0" . 'incorporationDate', '' . "\0" . 'Entities\\Company' . "\0" . 'dissolutionDate', '' . "\0" . 'Entities\\Company' . "\0" . 'companyCategory', '' . "\0" . 'Entities\\Company' . "\0" . 'jurisdiction', '' . "\0" . 'Entities\\Company' . "\0" . 'madeUpDate', '' . "\0" . 'Entities\\Company' . "\0" . 'premise', '' . "\0" . 'Entities\\Company' . "\0" . 'street', '' . "\0" . 'Entities\\Company' . "\0" . 'thoroughfare', '' . "\0" . 'Entities\\Company' . "\0" . 'postTown', '' . "\0" . 'Entities\\Company' . "\0" . 'county', '' . "\0" . 'Entities\\Company' . "\0" . 'country', '' . "\0" . 'Entities\\Company' . "\0" . 'postcode', '' . "\0" . 'Entities\\Company' . "\0" . 'careOfName', '' . "\0" . 'Entities\\Company' . "\0" . 'poBox', '' . "\0" . 'Entities\\Company' . "\0" . 'sailPremise', '' . "\0" . 'Entities\\Company' . "\0" . 'sailStreet', '' . "\0" . 'Entities\\Company' . "\0" . 'sailThoroughfare', '' . "\0" . 'Entities\\Company' . "\0" . 'sailPostTown', '' . "\0" . 'Entities\\Company' . "\0" . 'sailCounty', '' . "\0" . 'Entities\\Company' . "\0" . 'sailCountry', '' . "\0" . 'Entities\\Company' . "\0" . 'sailPostcode', '' . "\0" . 'Entities\\Company' . "\0" . 'sailCareOfName', '' . "\0" . 'Entities\\Company' . "\0" . 'sailPoBox', '' . "\0" . 'Entities\\Company' . "\0" . 'sicCode1', '' . "\0" . 'Entities\\Company' . "\0" . 'sicCode2', '' . "\0" . 'Entities\\Company' . "\0" . 'sicCode3', '' . "\0" . 'Entities\\Company' . "\0" . 'sicCode4', '' . "\0" . 'Entities\\Company' . "\0" . 'sicDescription', '' . "\0" . 'Entities\\Company' . "\0" . 'companyStatus', '' . "\0" . 'Entities\\Company' . "\0" . 'countryOfOrigin', '' . "\0" . 'Entities\\Company' . "\0" . 'accountsRefDate', '' . "\0" . 'Entities\\Company' . "\0" . 'accountsOverdue', '' . "\0" . 'Entities\\Company' . "\0" . 'accountsNextPeriodStartDate', '' . "\0" . 'Entities\\Company' . "\0" . 'accountsNextPeriodEndDate', '' . "\0" . 'Entities\\Company' . "\0" . 'accountsNextDueDate', '' . "\0" . 'Entities\\Company' . "\0" . 'accountsLastType', '' . "\0" . 'Entities\\Company' . "\0" . 'accountsLastPeriodStartDate', '' . "\0" . 'Entities\\Company' . "\0" . 'accountsLastMadeUpDate', '' . "\0" . 'Entities\\Company' . "\0" . 'returnsNextMakeUpDate', '' . "\0" . 'Entities\\Company' . "\0" . 'returnsNextDueDate', '' . "\0" . 'Entities\\Company' . "\0" . 'returnsLastMadeUpDate', '' . "\0" . 'Entities\\Company' . "\0" . 'returnsOverdue', '' . "\0" . 'Entities\\Company' . "\0" . 'dcaId', '' . "\0" . 'Entities\\Company' . "\0" . 'registeredOfficeId', '' . "\0" . 'Entities\\Company' . "\0" . 'serviceAddressId', '' . "\0" . 'Entities\\Company' . "\0" . 'nomineeDirectorId', '' . "\0" . 'Entities\\Company' . "\0" . 'nomineeSecretaryId', '' . "\0" . 'Entities\\Company' . "\0" . 'nomineeSubscriberId', '' . "\0" . 'Entities\\Company' . "\0" . 'annualReturnId', '' . "\0" . 'Entities\\Company' . "\0" . 'changeNameId', '' . "\0" . 'Entities\\Company' . "\0" . 'ereminderId', '' . "\0" . 'Entities\\Company' . "\0" . 'documentDate', '' . "\0" . 'Entities\\Company' . "\0" . 'documentId', '' . "\0" . 'Entities\\Company' . "\0" . 'acceptedDate', '' . "\0" . 'Entities\\Company' . "\0" . 'cashBackAmount', '' . "\0" . 'Entities\\Company' . "\0" . 'noPscReason', '' . "\0" . 'Entities\\Company' . "\0" . 'registeredEmailAddress', '' . "\0" . 'Entities\\Company' . "\0" . 'locked', '' . "\0" . 'Entities\\Company' . "\0" . 'hidden', '' . "\0" . 'Entities\\Company' . "\0" . 'deleted', '' . "\0" . 'Entities\\Company' . "\0" . 'etag', '' . "\0" . 'Entities\\Company' . "\0" . 'dateLastSynced', '' . "\0" . 'Entities\\Company' . "\0" . 'dtc', '' . "\0" . 'Entities\\Company' . "\0" . 'dtm', '' . "\0" . 'Entities\\Company' . "\0" . 'services', '' . "\0" . 'Entities\\Company' . "\0" . 'customer', '' . "\0" . 'Entities\\Company' . "\0" . 'order', '' . "\0" . 'Entities\\Company' . "\0" . 'formSubmissions', '' . "\0" . 'Entities\\Company' . "\0" . 'registerMembers', '' . "\0" . 'Entities\\Company' . "\0" . 'toolkitOffers', '' . "\0" . 'Entities\\Company' . "\0" . 'settings', '' . "\0" . 'Entities\\Company' . "\0" . 'businessPhoneOption'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (Company $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load(): void
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized(): bool
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized): void
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null): void
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer(): ?\Closure
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null): void
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner(): ?\Closure
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties(): array
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function getId(): ?int
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getId', []);

        return parent::getId();
    }

    /**
     * {@inheritDoc}
     */
    public function setId(int $id): \Entities\Company
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setId', [$id]);

        return parent::setId($id);
    }

    /**
     * {@inheritDoc}
     */
    public function getCompanyId()
    {
        if ($this->__isInitialized__ === false) {
            return (int)  parent::getCompanyId();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCompanyId', []);

        return parent::getCompanyId();
    }

    /**
     * {@inheritDoc}
     */
    public function isIncorporated(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isIncorporated', []);

        return parent::isIncorporated();
    }

    /**
     * {@inheritDoc}
     */
    public function isImported()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isImported', []);

        return parent::isImported();
    }

    /**
     * {@inheritDoc}
     */
    public function getProductId()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getProductId', []);

        return parent::getProductId();
    }

    /**
     * {@inheritDoc}
     */
    public function setProductId($productId)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setProductId', [$productId]);

        return parent::setProductId($productId);
    }

    /**
     * {@inheritDoc}
     */
    public function getIsCertificatePrinted()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getIsCertificatePrinted', []);

        return parent::getIsCertificatePrinted();
    }

    /**
     * {@inheritDoc}
     */
    public function setIsCertificatePrinted($isCertificatePrinted)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setIsCertificatePrinted', [$isCertificatePrinted]);

        return parent::setIsCertificatePrinted($isCertificatePrinted);
    }

    /**
     * {@inheritDoc}
     */
    public function getIsBronzeCoverLetterPrinted()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getIsBronzeCoverLetterPrinted', []);

        return parent::getIsBronzeCoverLetterPrinted();
    }

    /**
     * {@inheritDoc}
     */
    public function setIsBronzeCoverLetterPrinted($isBronzeCoverLetterPrinted)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setIsBronzeCoverLetterPrinted', [$isBronzeCoverLetterPrinted]);

        return parent::setIsBronzeCoverLetterPrinted($isBronzeCoverLetterPrinted);
    }

    /**
     * {@inheritDoc}
     */
    public function getIsMaPrinted()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getIsMaPrinted', []);

        return parent::getIsMaPrinted();
    }

    /**
     * {@inheritDoc}
     */
    public function setIsMaPrinted($isMaPrinted)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setIsMaPrinted', [$isMaPrinted]);

        return parent::setIsMaPrinted($isMaPrinted);
    }

    /**
     * {@inheritDoc}
     */
    public function getIsMaCoverLetterPrinted()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getIsMaCoverLetterPrinted', []);

        return parent::getIsMaCoverLetterPrinted();
    }

    /**
     * {@inheritDoc}
     */
    public function setIsMaCoverLetterPrinted($isMaCoverLetterPrinted)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setIsMaCoverLetterPrinted', [$isMaCoverLetterPrinted]);

        return parent::setIsMaCoverLetterPrinted($isMaCoverLetterPrinted);
    }

    /**
     * {@inheritDoc}
     */
    public function getCompanyName()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCompanyName', []);

        return parent::getCompanyName();
    }

    /**
     * {@inheritDoc}
     */
    public function setCompanyName($companyName)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCompanyName', [$companyName]);

        return parent::setCompanyName($companyName);
    }

    /**
     * {@inheritDoc}
     */
    public function getCompanyNumber()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCompanyNumber', []);

        return parent::getCompanyNumber();
    }

    /**
     * {@inheritDoc}
     */
    public function setCompanyNumber($companyNumber)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCompanyNumber', [$companyNumber]);

        return parent::setCompanyNumber($companyNumber);
    }

    /**
     * {@inheritDoc}
     */
    public function getAuthenticationCode()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAuthenticationCode', []);

        return parent::getAuthenticationCode();
    }

    /**
     * {@inheritDoc}
     */
    public function setAuthenticationCode($authenticationCode)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setAuthenticationCode', [$authenticationCode]);

        return parent::setAuthenticationCode($authenticationCode);
    }

    /**
     * {@inheritDoc}
     */
    public function getIncorporationDate(): ?\Utils\Date
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getIncorporationDate', []);

        return parent::getIncorporationDate();
    }

    /**
     * {@inheritDoc}
     */
    public function setIncorporationDate(\Utils\Date $incorporationDate)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setIncorporationDate', [$incorporationDate]);

        return parent::setIncorporationDate($incorporationDate);
    }

    /**
     * {@inheritDoc}
     */
    public function getDissolutionDate()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDissolutionDate', []);

        return parent::getDissolutionDate();
    }

    /**
     * {@inheritDoc}
     */
    public function setDissolutionDate(\DateTime $dissolutionDate = NULL)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDissolutionDate', [$dissolutionDate]);

        return parent::setDissolutionDate($dissolutionDate);
    }

    /**
     * {@inheritDoc}
     */
    public function getCompanyCategory()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCompanyCategory', []);

        return parent::getCompanyCategory();
    }

    /**
     * {@inheritDoc}
     */
    public function setCompanyCategory($companyCategory)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCompanyCategory', [$companyCategory]);

        return parent::setCompanyCategory($companyCategory);
    }

    /**
     * {@inheritDoc}
     */
    public function getJurisdiction()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getJurisdiction', []);

        return parent::getJurisdiction();
    }

    /**
     * {@inheritDoc}
     */
    public function setJurisdiction($jurisdiction)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setJurisdiction', [$jurisdiction]);

        return parent::setJurisdiction($jurisdiction);
    }

    /**
     * {@inheritDoc}
     */
    public function getMadeUpDate(): \DateTime
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getMadeUpDate', []);

        return parent::getMadeUpDate();
    }

    /**
     * {@inheritDoc}
     */
    public function setMadeUpDate(\DateTime $madeUpDate)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setMadeUpDate', [$madeUpDate]);

        return parent::setMadeUpDate($madeUpDate);
    }

    /**
     * {@inheritDoc}
     */
    public function getPremise(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPremise', []);

        return parent::getPremise();
    }

    /**
     * {@inheritDoc}
     */
    public function setPremise($premise)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setPremise', [$premise]);

        return parent::setPremise($premise);
    }

    /**
     * {@inheritDoc}
     */
    public function getStreet(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getStreet', []);

        return parent::getStreet();
    }

    /**
     * {@inheritDoc}
     */
    public function setStreet($street)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setStreet', [$street]);

        return parent::setStreet($street);
    }

    /**
     * {@inheritDoc}
     */
    public function getThoroughfare(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getThoroughfare', []);

        return parent::getThoroughfare();
    }

    /**
     * {@inheritDoc}
     */
    public function setThoroughfare($thoroughfare)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setThoroughfare', [$thoroughfare]);

        return parent::setThoroughfare($thoroughfare);
    }

    /**
     * {@inheritDoc}
     */
    public function getPostTown(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPostTown', []);

        return parent::getPostTown();
    }

    /**
     * {@inheritDoc}
     */
    public function setPostTown($postTown)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setPostTown', [$postTown]);

        return parent::setPostTown($postTown);
    }

    /**
     * {@inheritDoc}
     */
    public function getCounty(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCounty', []);

        return parent::getCounty();
    }

    /**
     * {@inheritDoc}
     */
    public function setCounty($county)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCounty', [$county]);

        return parent::setCounty($county);
    }

    /**
     * {@inheritDoc}
     */
    public function getCountry(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCountry', []);

        return parent::getCountry();
    }

    /**
     * {@inheritDoc}
     */
    public function setCountry($country)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCountry', [$country]);

        return parent::setCountry($country);
    }

    /**
     * {@inheritDoc}
     */
    public function getPostcode(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPostcode', []);

        return parent::getPostcode();
    }

    /**
     * {@inheritDoc}
     */
    public function setPostcode($postcode)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setPostcode', [$postcode]);

        return parent::setPostcode($postcode);
    }

    /**
     * {@inheritDoc}
     */
    public function getCareOfName()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCareOfName', []);

        return parent::getCareOfName();
    }

    /**
     * {@inheritDoc}
     */
    public function setCareOfName($careOfName)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCareOfName', [$careOfName]);

        return parent::setCareOfName($careOfName);
    }

    /**
     * {@inheritDoc}
     */
    public function getPoBox()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPoBox', []);

        return parent::getPoBox();
    }

    /**
     * {@inheritDoc}
     */
    public function setPoBox($poBox)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setPoBox', [$poBox]);

        return parent::setPoBox($poBox);
    }

    /**
     * {@inheritDoc}
     */
    public function getSailPremise()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSailPremise', []);

        return parent::getSailPremise();
    }

    /**
     * {@inheritDoc}
     */
    public function setSailPremise($sailPremise)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setSailPremise', [$sailPremise]);

        return parent::setSailPremise($sailPremise);
    }

    /**
     * {@inheritDoc}
     */
    public function getSailStreet()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSailStreet', []);

        return parent::getSailStreet();
    }

    /**
     * {@inheritDoc}
     */
    public function setSailStreet($sailStreet)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setSailStreet', [$sailStreet]);

        return parent::setSailStreet($sailStreet);
    }

    /**
     * {@inheritDoc}
     */
    public function getSailThoroughfare()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSailThoroughfare', []);

        return parent::getSailThoroughfare();
    }

    /**
     * {@inheritDoc}
     */
    public function setSailThoroughfare($sailThoroughfare)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setSailThoroughfare', [$sailThoroughfare]);

        return parent::setSailThoroughfare($sailThoroughfare);
    }

    /**
     * {@inheritDoc}
     */
    public function getSailPostTown()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSailPostTown', []);

        return parent::getSailPostTown();
    }

    /**
     * {@inheritDoc}
     */
    public function setSailPostTown($sailPostTown)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setSailPostTown', [$sailPostTown]);

        return parent::setSailPostTown($sailPostTown);
    }

    /**
     * {@inheritDoc}
     */
    public function getSailCounty()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSailCounty', []);

        return parent::getSailCounty();
    }

    /**
     * {@inheritDoc}
     */
    public function setSailCounty($sailCounty)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setSailCounty', [$sailCounty]);

        return parent::setSailCounty($sailCounty);
    }

    /**
     * {@inheritDoc}
     */
    public function getSailCountry()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSailCountry', []);

        return parent::getSailCountry();
    }

    /**
     * {@inheritDoc}
     */
    public function setSailCountry($sailCountry)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setSailCountry', [$sailCountry]);

        return parent::setSailCountry($sailCountry);
    }

    /**
     * {@inheritDoc}
     */
    public function getSailPostcode()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSailPostcode', []);

        return parent::getSailPostcode();
    }

    /**
     * {@inheritDoc}
     */
    public function setSailPostcode($sailPostcode)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setSailPostcode', [$sailPostcode]);

        return parent::setSailPostcode($sailPostcode);
    }

    /**
     * {@inheritDoc}
     */
    public function getSailCareOfName()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSailCareOfName', []);

        return parent::getSailCareOfName();
    }

    /**
     * {@inheritDoc}
     */
    public function setSailCareOfName($sailCareOfName)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setSailCareOfName', [$sailCareOfName]);

        return parent::setSailCareOfName($sailCareOfName);
    }

    /**
     * {@inheritDoc}
     */
    public function getSailPoBox()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSailPoBox', []);

        return parent::getSailPoBox();
    }

    /**
     * {@inheritDoc}
     */
    public function setSailPoBox($sailPoBox)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setSailPoBox', [$sailPoBox]);

        return parent::setSailPoBox($sailPoBox);
    }

    /**
     * {@inheritDoc}
     */
    public function getSicCode1()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSicCode1', []);

        return parent::getSicCode1();
    }

    /**
     * {@inheritDoc}
     */
    public function setSicCode1($sicCode1)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setSicCode1', [$sicCode1]);

        return parent::setSicCode1($sicCode1);
    }

    /**
     * {@inheritDoc}
     */
    public function getSicCode2()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSicCode2', []);

        return parent::getSicCode2();
    }

    /**
     * {@inheritDoc}
     */
    public function setSicCode2($sicCode2)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setSicCode2', [$sicCode2]);

        return parent::setSicCode2($sicCode2);
    }

    /**
     * {@inheritDoc}
     */
    public function getSicCode3()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSicCode3', []);

        return parent::getSicCode3();
    }

    /**
     * {@inheritDoc}
     */
    public function setSicCode3($sicCode3)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setSicCode3', [$sicCode3]);

        return parent::setSicCode3($sicCode3);
    }

    /**
     * {@inheritDoc}
     */
    public function getSicCode4()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSicCode4', []);

        return parent::getSicCode4();
    }

    /**
     * {@inheritDoc}
     */
    public function setSicCode4($sicCode4)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setSicCode4', [$sicCode4]);

        return parent::setSicCode4($sicCode4);
    }

    /**
     * {@inheritDoc}
     */
    public function getSicCodes()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSicCodes', []);

        return parent::getSicCodes();
    }

    /**
     * {@inheritDoc}
     */
    public function setSicCodes(array $sicCodes)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setSicCodes', [$sicCodes]);

        return parent::setSicCodes($sicCodes);
    }

    /**
     * {@inheritDoc}
     */
    public function getSicDescription()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSicDescription', []);

        return parent::getSicDescription();
    }

    /**
     * {@inheritDoc}
     */
    public function setSicDescription($sicDescription)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setSicDescription', [$sicDescription]);

        return parent::setSicDescription($sicDescription);
    }

    /**
     * {@inheritDoc}
     */
    public function getCompanyStatus()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCompanyStatus', []);

        return parent::getCompanyStatus();
    }

    /**
     * {@inheritDoc}
     */
    public function getStatus()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getStatus', []);

        return parent::getStatus();
    }

    /**
     * {@inheritDoc}
     */
    public function setCompanyStatus($companyStatus)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCompanyStatus', [$companyStatus]);

        return parent::setCompanyStatus($companyStatus);
    }

    /**
     * {@inheritDoc}
     */
    public function getComputedStatus()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getComputedStatus', []);

        return parent::getComputedStatus();
    }

    /**
     * {@inheritDoc}
     */
    public function getCountryOfOrigin()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCountryOfOrigin', []);

        return parent::getCountryOfOrigin();
    }

    /**
     * {@inheritDoc}
     */
    public function setCountryOfOrigin($countryOfOrigin)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCountryOfOrigin', [$countryOfOrigin]);

        return parent::setCountryOfOrigin($countryOfOrigin);
    }

    /**
     * {@inheritDoc}
     */
    public function getAccountsRefDate()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAccountsRefDate', []);

        return parent::getAccountsRefDate();
    }

    /**
     * {@inheritDoc}
     */
    public function setAccountsRefDate($accountsRefDate)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setAccountsRefDate', [$accountsRefDate]);

        return parent::setAccountsRefDate($accountsRefDate);
    }

    /**
     * {@inheritDoc}
     */
    public function getAccountsOverdue()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAccountsOverdue', []);

        return parent::getAccountsOverdue();
    }

    /**
     * {@inheritDoc}
     */
    public function setAccountsOverdue(bool $accountsOverdue = NULL)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setAccountsOverdue', [$accountsOverdue]);

        return parent::setAccountsOverdue($accountsOverdue);
    }

    /**
     * {@inheritDoc}
     */
    public function getAccountsNextPeriodStartDate()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAccountsNextPeriodStartDate', []);

        return parent::getAccountsNextPeriodStartDate();
    }

    /**
     * {@inheritDoc}
     */
    public function setAccountsNextPeriodStartDate(\DateTime $accountsNextPeriodStartDate = NULL)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setAccountsNextPeriodStartDate', [$accountsNextPeriodStartDate]);

        return parent::setAccountsNextPeriodStartDate($accountsNextPeriodStartDate);
    }

    /**
     * {@inheritDoc}
     */
    public function getAccountsNextPeriodEndDate()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAccountsNextPeriodEndDate', []);

        return parent::getAccountsNextPeriodEndDate();
    }

    /**
     * {@inheritDoc}
     */
    public function setAccountsNextPeriodEndDate(\DateTime $accountsNextPeriodEndDate = NULL)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setAccountsNextPeriodEndDate', [$accountsNextPeriodEndDate]);

        return parent::setAccountsNextPeriodEndDate($accountsNextPeriodEndDate);
    }

    /**
     * {@inheritDoc}
     */
    public function getAccountsNextDueDate()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAccountsNextDueDate', []);

        return parent::getAccountsNextDueDate();
    }

    /**
     * {@inheritDoc}
     */
    public function setAccountsNextDueDate(\DateTime $accountsNextDueDate = NULL)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setAccountsNextDueDate', [$accountsNextDueDate]);

        return parent::setAccountsNextDueDate($accountsNextDueDate);
    }

    /**
     * {@inheritDoc}
     */
    public function getAccountsLastType()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAccountsLastType', []);

        return parent::getAccountsLastType();
    }

    /**
     * {@inheritDoc}
     */
    public function setAccountsLastType(string $accountsLastType = NULL)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setAccountsLastType', [$accountsLastType]);

        return parent::setAccountsLastType($accountsLastType);
    }

    /**
     * {@inheritDoc}
     */
    public function getAccountsLastPeriodStartDate()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAccountsLastPeriodStartDate', []);

        return parent::getAccountsLastPeriodStartDate();
    }

    /**
     * {@inheritDoc}
     */
    public function setAccountsLastPeriodStartDate(\DateTime $accountsLastPeriodStartDate = NULL)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setAccountsLastPeriodStartDate', [$accountsLastPeriodStartDate]);

        return parent::setAccountsLastPeriodStartDate($accountsLastPeriodStartDate);
    }

    /**
     * {@inheritDoc}
     */
    public function getAccountsLastMadeUpDate()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAccountsLastMadeUpDate', []);

        return parent::getAccountsLastMadeUpDate();
    }

    /**
     * {@inheritDoc}
     */
    public function setAccountsLastMadeUpDate(\DateTime $accountsLastMadeUpDate = NULL)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setAccountsLastMadeUpDate', [$accountsLastMadeUpDate]);

        return parent::setAccountsLastMadeUpDate($accountsLastMadeUpDate);
    }

    /**
     * {@inheritDoc}
     */
    public function getReturnsNextMakeUpDate()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getReturnsNextMakeUpDate', []);

        return parent::getReturnsNextMakeUpDate();
    }

    /**
     * {@inheritDoc}
     */
    public function setReturnsNextMakeUpDate(\DateTime $returnsNextMakeUpDate = NULL)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setReturnsNextMakeUpDate', [$returnsNextMakeUpDate]);

        return parent::setReturnsNextMakeUpDate($returnsNextMakeUpDate);
    }

    /**
     * {@inheritDoc}
     */
    public function getReturnsNextDueDate()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getReturnsNextDueDate', []);

        return parent::getReturnsNextDueDate();
    }

    /**
     * {@inheritDoc}
     */
    public function setReturnsNextDueDate(\DateTime $returnsNextDueDate = NULL)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setReturnsNextDueDate', [$returnsNextDueDate]);

        return parent::setReturnsNextDueDate($returnsNextDueDate);
    }

    /**
     * {@inheritDoc}
     */
    public function getReturnsLastMadeUpDate()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getReturnsLastMadeUpDate', []);

        return parent::getReturnsLastMadeUpDate();
    }

    /**
     * {@inheritDoc}
     */
    public function setReturnsLastMadeUpDate(\DateTime $returnsLastMadeUpDate = NULL)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setReturnsLastMadeUpDate', [$returnsLastMadeUpDate]);

        return parent::setReturnsLastMadeUpDate($returnsLastMadeUpDate);
    }

    /**
     * {@inheritDoc}
     */
    public function getReturnsOverdue()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getReturnsOverdue', []);

        return parent::getReturnsOverdue();
    }

    /**
     * {@inheritDoc}
     */
    public function setReturnsOverdue(bool $returnsOverdue = NULL)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setReturnsOverdue', [$returnsOverdue]);

        return parent::setReturnsOverdue($returnsOverdue);
    }

    /**
     * {@inheritDoc}
     */
    public function getDcaId()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDcaId', []);

        return parent::getDcaId();
    }

    /**
     * {@inheritDoc}
     */
    public function setDcaId($dcaId)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDcaId', [$dcaId]);

        return parent::setDcaId($dcaId);
    }

    /**
     * {@inheritDoc}
     */
    public function getRegisteredOfficeId()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getRegisteredOfficeId', []);

        return parent::getRegisteredOfficeId();
    }

    /**
     * {@inheritDoc}
     */
    public function setRegisteredOfficeId($registeredOfficeId)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setRegisteredOfficeId', [$registeredOfficeId]);

        return parent::setRegisteredOfficeId($registeredOfficeId);
    }

    /**
     * {@inheritDoc}
     */
    public function getServiceAddressId()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getServiceAddressId', []);

        return parent::getServiceAddressId();
    }

    /**
     * {@inheritDoc}
     */
    public function setServiceAddressId($serviceAddressId)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setServiceAddressId', [$serviceAddressId]);

        return parent::setServiceAddressId($serviceAddressId);
    }

    /**
     * {@inheritDoc}
     */
    public function getNomineeDirectorId()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNomineeDirectorId', []);

        return parent::getNomineeDirectorId();
    }

    /**
     * {@inheritDoc}
     */
    public function setNomineeDirectorId($nomineeDirectorId)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNomineeDirectorId', [$nomineeDirectorId]);

        return parent::setNomineeDirectorId($nomineeDirectorId);
    }

    /**
     * {@inheritDoc}
     */
    public function getNomineeSecretaryId()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNomineeSecretaryId', []);

        return parent::getNomineeSecretaryId();
    }

    /**
     * {@inheritDoc}
     */
    public function setNomineeSecretaryId($nomineeSecretaryId)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNomineeSecretaryId', [$nomineeSecretaryId]);

        return parent::setNomineeSecretaryId($nomineeSecretaryId);
    }

    /**
     * {@inheritDoc}
     */
    public function getNomineeSubscriberId()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNomineeSubscriberId', []);

        return parent::getNomineeSubscriberId();
    }

    /**
     * {@inheritDoc}
     */
    public function setNomineeSubscriberId($nomineeSubscriberId)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNomineeSubscriberId', [$nomineeSubscriberId]);

        return parent::setNomineeSubscriberId($nomineeSubscriberId);
    }

    /**
     * {@inheritDoc}
     */
    public function getAnnualReturnId()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAnnualReturnId', []);

        return parent::getAnnualReturnId();
    }

    /**
     * {@inheritDoc}
     */
    public function setAnnualReturnId($annualReturnId)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setAnnualReturnId', [$annualReturnId]);

        return parent::setAnnualReturnId($annualReturnId);
    }

    /**
     * {@inheritDoc}
     */
    public function getChangeNameId()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getChangeNameId', []);

        return parent::getChangeNameId();
    }

    /**
     * {@inheritDoc}
     */
    public function setChangeNameId($changeNameId)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setChangeNameId', [$changeNameId]);

        return parent::setChangeNameId($changeNameId);
    }

    /**
     * {@inheritDoc}
     */
    public function getEreminderId()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getEreminderId', []);

        return parent::getEreminderId();
    }

    /**
     * {@inheritDoc}
     */
    public function setEreminderId($ereminderId)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setEreminderId', [$ereminderId]);

        return parent::setEreminderId($ereminderId);
    }

    /**
     * {@inheritDoc}
     */
    public function getDocumentDate()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDocumentDate', []);

        return parent::getDocumentDate();
    }

    /**
     * {@inheritDoc}
     */
    public function setDocumentDate(\DateTime $documentDate)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDocumentDate', [$documentDate]);

        return parent::setDocumentDate($documentDate);
    }

    /**
     * {@inheritDoc}
     */
    public function getDocumentId()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDocumentId', []);

        return parent::getDocumentId();
    }

    /**
     * {@inheritDoc}
     */
    public function setDocumentId($documentId)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDocumentId', [$documentId]);

        return parent::setDocumentId($documentId);
    }

    /**
     * {@inheritDoc}
     */
    public function getAcceptedDate()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAcceptedDate', []);

        return parent::getAcceptedDate();
    }

    /**
     * {@inheritDoc}
     */
    public function setAcceptedDate(\DateTime $acceptedDate)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setAcceptedDate', [$acceptedDate]);

        return parent::setAcceptedDate($acceptedDate);
    }

    /**
     * {@inheritDoc}
     */
    public function getCashBackAmount()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCashBackAmount', []);

        return parent::getCashBackAmount();
    }

    /**
     * {@inheritDoc}
     */
    public function setCashBackAmount($cashBackAmount)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCashBackAmount', [$cashBackAmount]);

        return parent::setCashBackAmount($cashBackAmount);
    }

    /**
     * {@inheritDoc}
     */
    public function getNoPscReason()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNoPscReason', []);

        return parent::getNoPscReason();
    }

    /**
     * {@inheritDoc}
     */
    public function hasNoPscReason(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasNoPscReason', []);

        return parent::hasNoPscReason();
    }

    /**
     * {@inheritDoc}
     */
    public function setNoPscReason($noPscReason)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setNoPscReason', [$noPscReason]);

        return parent::setNoPscReason($noPscReason);
    }

    /**
     * {@inheritDoc}
     */
    public function getLocked()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLocked', []);

        return parent::getLocked();
    }

    /**
     * {@inheritDoc}
     */
    public function isLocked()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isLocked', []);

        return parent::isLocked();
    }

    /**
     * {@inheritDoc}
     */
    public function setLocked($locked)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setLocked', [$locked]);

        return parent::setLocked($locked);
    }

    /**
     * {@inheritDoc}
     */
    public function getHidden()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getHidden', []);

        return parent::getHidden();
    }

    /**
     * {@inheritDoc}
     */
    public function setHidden($hidden)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setHidden', [$hidden]);

        return parent::setHidden($hidden);
    }

    /**
     * {@inheritDoc}
     */
    public function getDeleted()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDeleted', []);

        return parent::getDeleted();
    }

    /**
     * {@inheritDoc}
     */
    public function getEtag()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getEtag', []);

        return parent::getEtag();
    }

    /**
     * {@inheritDoc}
     */
    public function setEtag(string $etag = NULL)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setEtag', [$etag]);

        return parent::setEtag($etag);
    }

    /**
     * {@inheritDoc}
     */
    public function getDateLastSynced()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDateLastSynced', []);

        return parent::getDateLastSynced();
    }

    /**
     * {@inheritDoc}
     */
    public function setDateLastSynced(\DateTime $dateTime = NULL)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDateLastSynced', [$dateTime]);

        return parent::setDateLastSynced($dateTime);
    }

    /**
     * {@inheritDoc}
     */
    public function setDeleted($deleted)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDeleted', [$deleted]);

        return parent::setDeleted($deleted);
    }

    /**
     * {@inheritDoc}
     */
    public function getDtc()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDtc', []);

        return parent::getDtc();
    }

    /**
     * {@inheritDoc}
     */
    public function setDtc(\DateTime $dtc)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDtc', [$dtc]);

        return parent::setDtc($dtc);
    }

    /**
     * {@inheritDoc}
     */
    public function getDtm()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDtm', []);

        return parent::getDtm();
    }

    /**
     * {@inheritDoc}
     */
    public function setDtm(\DateTime $dtm)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDtm', [$dtm]);

        return parent::setDtm($dtm);
    }

    /**
     * {@inheritDoc}
     */
    public function getServices()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getServices', []);

        return parent::getServices();
    }

    /**
     * {@inheritDoc}
     */
    public function getGroupedServices()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getGroupedServices', []);

        return parent::getGroupedServices();
    }

    /**
     * {@inheritDoc}
     */
    public function getEnabledParentServices()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getEnabledParentServices', []);

        return parent::getEnabledParentServices();
    }

    /**
     * {@inheritDoc}
     */
    public function getEnabledParentServicesWithType($typeId)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getEnabledParentServicesWithType', [$typeId]);

        return parent::getEnabledParentServicesWithType($typeId);
    }

    /**
     * {@inheritDoc}
     */
    public function getEnabledActivatedParentServices()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getEnabledActivatedParentServices', []);

        return parent::getEnabledActivatedParentServices();
    }

    /**
     * {@inheritDoc}
     */
    public function getEnabledNotActivatedParentServices()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getEnabledNotActivatedParentServices', []);

        return parent::getEnabledNotActivatedParentServices();
    }

    /**
     * {@inheritDoc}
     */
    public function getActiveCorePackageService(): ?\Entities\Service
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getActiveCorePackageService', []);

        return parent::getActiveCorePackageService();
    }

    /**
     * {@inheritDoc}
     */
    public function getActiveServiceOfType($serviceTypeId)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getActiveServiceOfType', [$serviceTypeId]);

        return parent::getActiveServiceOfType($serviceTypeId);
    }

    /**
     * {@inheritDoc}
     */
    public function isUsingMsgPostcodeWithoutService()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isUsingMsgPostcodeWithoutService', []);

        return parent::isUsingMsgPostcodeWithoutService();
    }

    /**
     * {@inheritDoc}
     */
    public function isNotUsingMsgPostcodeWithService()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isNotUsingMsgPostcodeWithService', []);

        return parent::isNotUsingMsgPostcodeWithService();
    }

    /**
     * {@inheritDoc}
     */
    public function hasActiveServiceOfType($serviceTypeId): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasActiveServiceOfType', [$serviceTypeId]);

        return parent::hasActiveServiceOfType($serviceTypeId);
    }

    /**
     * {@inheritDoc}
     */
    public function getNotActivatedServicesOfType($serviceTypeId)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNotActivatedServicesOfType', [$serviceTypeId]);

        return parent::getNotActivatedServicesOfType($serviceTypeId);
    }

    /**
     * {@inheritDoc}
     */
    public function hasNotActivatedServiceOfType($serviceTypeId)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasNotActivatedServiceOfType', [$serviceTypeId]);

        return parent::hasNotActivatedServiceOfType($serviceTypeId);
    }

    /**
     * {@inheritDoc}
     */
    public function setServices($services)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setServices', [$services]);

        return parent::setServices($services);
    }

    /**
     * {@inheritDoc}
     */
    public function getCustomer(): \UserModule\Contracts\ICustomer
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCustomer', []);

        return parent::getCustomer();
    }

    /**
     * {@inheritDoc}
     */
    public function setCustomer(\Entities\Customer $customer)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCustomer', [$customer]);

        return parent::setCustomer($customer);
    }

    /**
     * {@inheritDoc}
     */
    public function getOrder()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getOrder', []);

        return parent::getOrder();
    }

    /**
     * {@inheritDoc}
     */
    public function setOrder(\Entities\Order $order)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setOrder', [$order]);

        return parent::setOrder($order);
    }

    /**
     * {@inheritDoc}
     */
    public function getFormSubmissions()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getFormSubmissions', []);

        return parent::getFormSubmissions();
    }

    /**
     * {@inheritDoc}
     */
    public function getLatestFormSubmissionOfType(string $type, bool $rejected = NULL)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLatestFormSubmissionOfType', [$type, $rejected]);

        return parent::getLatestFormSubmissionOfType($type, $rejected);
    }

    /**
     * {@inheritDoc}
     */
    public function getIncorporationFormSubmission()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getIncorporationFormSubmission', []);

        return parent::getIncorporationFormSubmission();
    }

    /**
     * {@inheritDoc}
     */
    public function addFormSubmission(\Entities\CompanyHouse\FormSubmission $formSubmission)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'addFormSubmission', [$formSubmission]);

        return parent::addFormSubmission($formSubmission);
    }

    /**
     * {@inheritDoc}
     */
    public function setFormSubmissions($formSubmissions)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setFormSubmissions', [$formSubmissions]);

        return parent::setFormSubmissions($formSubmissions);
    }

    /**
     * {@inheritDoc}
     */
    public function isEqual(\Entities\Company $company)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isEqual', [$company]);

        return parent::isEqual($company);
    }

    /**
     * {@inheritDoc}
     */
    public function addService(\Entities\Service $service)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'addService', [$service]);

        return parent::addService($service);
    }

    /**
     * {@inheritDoc}
     */
    public function isLimitedBySharesType()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isLimitedBySharesType', []);

        return parent::isLimitedBySharesType();
    }

    /**
     * {@inheritDoc}
     */
    public function getRegisterMembers()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getRegisterMembers', []);

        return parent::getRegisterMembers();
    }

    /**
     * {@inheritDoc}
     */
    public function addRegisterMember(\Entities\Register\Member $registerMember)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'addRegisterMember', [$registerMember]);

        return parent::addRegisterMember($registerMember);
    }

    /**
     * {@inheritDoc}
     */
    public function hasServices()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasServices', []);

        return parent::hasServices();
    }

    /**
     * {@inheritDoc}
     */
    public function hasActiveService()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasActiveService', []);

        return parent::hasActiveService();
    }

    /**
     * {@inheritDoc}
     */
    public function hasEqualService(\Entities\Service $service)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasEqualService', [$service]);

        return parent::hasEqualService($service);
    }

    /**
     * {@inheritDoc}
     */
    public function getServicesByType($serviceTypeId)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getServicesByType', [$serviceTypeId]);

        return parent::getServicesByType($serviceTypeId);
    }

    /**
     * {@inheritDoc}
     */
    public function hasServiceByType(string $serviceTypeId): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasServiceByType', [$serviceTypeId]);

        return parent::hasServiceByType($serviceTypeId);
    }

    /**
     * {@inheritDoc}
     */
    public function getLastServiceByType($service)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLastServiceByType', [$service]);

        return parent::getLastServiceByType($service);
    }

    /**
     * {@inheritDoc}
     */
    public function getLastServiceByServiceTypeId(string $serviceTypId): \Entities\Service
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLastServiceByServiceTypeId', [$serviceTypId]);

        return parent::getLastServiceByServiceTypeId($serviceTypId);
    }

    /**
     * {@inheritDoc}
     */
    public function hasActiveRenewalServiceByType($service)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasActiveRenewalServiceByType', [$service]);

        return parent::hasActiveRenewalServiceByType($service);
    }

    /**
     * {@inheritDoc}
     */
    public function getActiveProductServices(array $types)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getActiveProductServices', [$types]);

        return parent::getActiveProductServices($types);
    }

    /**
     * {@inheritDoc}
     */
    public function getNonExpiredServicesOn(\DateTime $date)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNonExpiredServicesOn', [$date]);

        return parent::getNonExpiredServicesOn($date);
    }

    /**
     * {@inheritDoc}
     */
    public function getProductServicesByTypes(array $types)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getProductServicesByTypes', [$types]);

        return parent::getProductServicesByTypes($types);
    }

    /**
     * {@inheritDoc}
     */
    public function getLastPackageService($type)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLastPackageService', [$type]);

        return parent::getLastPackageService($type);
    }

    /**
     * {@inheritDoc}
     */
    public function getLastPackageServiceForTypes(array $types): ?\Entities\Service
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLastPackageServiceForTypes', [$types]);

        return parent::getLastPackageServiceForTypes($types);
    }

    /**
     * {@inheritDoc}
     */
    public function hasAuthenticationCode()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasAuthenticationCode', []);

        return parent::hasAuthenticationCode();
    }

    /**
     * {@inheritDoc}
     */
    public function getSelectedToolkitOffers()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSelectedToolkitOffers', []);

        return parent::getSelectedToolkitOffers();
    }

    /**
     * {@inheritDoc}
     */
    public function toolkitOffersAlreadyChosen()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'toolkitOffersAlreadyChosen', []);

        return parent::toolkitOffersAlreadyChosen();
    }

    /**
     * {@inheritDoc}
     */
    public function addToolkitOffer(\ToolkitOfferModule\Entities\CompanyToolkitOffer $offer)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'addToolkitOffer', [$offer]);

        return parent::addToolkitOffer($offer);
    }

    /**
     * {@inheritDoc}
     */
    public function clearToolkitOffers()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'clearToolkitOffers', []);

        return parent::clearToolkitOffers();
    }

    /**
     * {@inheritDoc}
     */
    public function isDissolved()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isDissolved', []);

        return parent::isDissolved();
    }

    /**
     * {@inheritDoc}
     */
    public function belongsToUkCustomer()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'belongsToUkCustomer', []);

        return parent::belongsToUkCustomer();
    }

    /**
     * {@inheritDoc}
     */
    public function canUseOurServiceAddress()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'canUseOurServiceAddress', []);

        return parent::canUseOurServiceAddress();
    }

    /**
     * {@inheritDoc}
     */
    public function hasAnActiveMailForwardingService(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasAnActiveMailForwardingService', []);

        return parent::hasAnActiveMailForwardingService();
    }

    /**
     * {@inheritDoc}
     */
    public function getCurrentServiceOfType($serviceTypeId)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCurrentServiceOfType', [$serviceTypeId]);

        return parent::getCurrentServiceOfType($serviceTypeId);
    }

    /**
     * {@inheritDoc}
     */
    public function isDeleted()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isDeleted', []);

        return parent::isDeleted();
    }

    /**
     * {@inheritDoc}
     */
    public function getSettings()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSettings', []);

        return parent::getSettings();
    }

    /**
     * {@inheritDoc}
     */
    public function addSetting(\CompanyModule\Entities\Settings\CompanySetting $setting)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'addSetting', [$setting]);

        return parent::addSetting($setting);
    }

    /**
     * {@inheritDoc}
     */
    public function belongsToWholesale()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'belongsToWholesale', []);

        return parent::belongsToWholesale();
    }

    /**
     * {@inheritDoc}
     */
    public function isByGuarType()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isByGuarType', []);

        return parent::isByGuarType();
    }

    /**
     * {@inheritDoc}
     */
    public function isLlpType()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isLlpType', []);

        return parent::isLlpType();
    }

    /**
     * {@inheritDoc}
     */
    public function getRegisteredOffice()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getRegisteredOffice', []);

        return parent::getRegisteredOffice();
    }

    /**
     * {@inheritDoc}
     */
    public function setRegisteredOffice(\CompaniesHouseModule\Entities\Address $address): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setRegisteredOffice', [$address]);

        parent::setRegisteredOffice($address);
    }

    /**
     * {@inheritDoc}
     */
    public function isActive(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isActive', []);

        return parent::isActive();
    }

    /**
     * {@inheritDoc}
     */
    public function isUsingMsgRegisteredOffice(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isUsingMsgRegisteredOffice', []);

        return parent::isUsingMsgRegisteredOffice();
    }

    /**
     * {@inheritDoc}
     */
    public function getName(): \CompanyModule\Domain\Company\CompanyName
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getName', []);

        return parent::getName();
    }

    /**
     * {@inheritDoc}
     */
    public function getNumber(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getNumber', []);

        return parent::getNumber();
    }

    /**
     * {@inheritDoc}
     */
    public function getCustomerEmail(): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCustomerEmail', []);

        return parent::getCustomerEmail();
    }

    /**
     * {@inheritDoc}
     */
    public function markAsDeleted()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'markAsDeleted', []);

        return parent::markAsDeleted();
    }

    /**
     * {@inheritDoc}
     */
    public function getServicesByIds(array $services): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getServicesByIds', [$services]);

        return parent::getServicesByIds($services);
    }

    /**
     * {@inheritDoc}
     */
    public function hasNominee(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasNominee', []);

        return parent::hasNominee();
    }

    /**
     * {@inheritDoc}
     */
    public function hasConfirmationStatementServiceActive(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasConfirmationStatementServiceActive', []);

        return parent::hasConfirmationStatementServiceActive();
    }

    /**
     * {@inheritDoc}
     */
    public function isEeig(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isEeig', []);

        return parent::isEeig();
    }

    /**
     * {@inheritDoc}
     */
    public function isUkEstablishment(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isUkEstablishment', []);

        return parent::isUkEstablishment();
    }

    /**
     * {@inheritDoc}
     */
    public function isLimitedPartnership(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isLimitedPartnership', []);

        return parent::isLimitedPartnership();
    }

    /**
     * {@inheritDoc}
     */
    public function getBusinessPhoneOption(): ?\CustomerModule\Entities\BusinessPhoneOption
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getBusinessPhoneOption', []);

        return parent::getBusinessPhoneOption();
    }

    /**
     * {@inheritDoc}
     */
    public function setBusinessPhoneOption(?\CustomerModule\Entities\BusinessPhoneOption $businessPhoneOption): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setBusinessPhoneOption', [$businessPhoneOption]);

        parent::setBusinessPhoneOption($businessPhoneOption);
    }

    /**
     * {@inheritDoc}
     */
    public function getPaymentGateway(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPaymentGateway', []);

        return parent::getPaymentGateway();
    }

    /**
     * {@inheritDoc}
     */
    public function getRegisteredEmailAddress(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getRegisteredEmailAddress', []);

        return parent::getRegisteredEmailAddress();
    }

    /**
     * {@inheritDoc}
     */
    public function setRegisteredEmailAddress(string $registeredEmailAddress): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setRegisteredEmailAddress', [$registeredEmailAddress]);

        parent::setRegisteredEmailAddress($registeredEmailAddress);
    }

    /**
     * {@inheritDoc}
     */
    public function hasMainFields(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasMainFields', []);

        return parent::hasMainFields();
    }

    /**
     * {@inheritDoc}
     */
    public function isBasicPackage(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isBasicPackage', []);

        return parent::isBasicPackage();
    }

    /**
     * {@inheritDoc}
     */
    public function jsonSerialize(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'jsonSerialize', []);

        return parent::jsonSerialize();
    }

    /**
     * {@inheritDoc}
     */
    public function getSimplifiedCompanyData(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSimplifiedCompanyData', []);

        return parent::getSimplifiedCompanyData();
    }

    /**
     * {@inheritDoc}
     */
    public function getActiveOrLatestMailboxService(): ?\Entities\Service
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getActiveOrLatestMailboxService', []);

        return parent::getActiveOrLatestMailboxService();
    }

    /**
     * {@inheritDoc}
     */
    public function getLatestMailboxService(): ?\Entities\Service
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLatestMailboxService', []);

        return parent::getLatestMailboxService();
    }

    /**
     * {@inheritDoc}
     */
    public function getActiveMailboxService(): ?\Entities\Service
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getActiveMailboxService', []);

        return parent::getActiveMailboxService();
    }

    /**
     * {@inheritDoc}
     */
    public function hasActiveMailboxService(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasActiveMailboxService', []);

        return parent::hasActiveMailboxService();
    }

    /**
     * {@inheritDoc}
     */
    public function getInactiveMailboxService(): ?\Entities\Service
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getInactiveMailboxService', []);

        return parent::getInactiveMailboxService();
    }

    /**
     * {@inheritDoc}
     */
    public function hasInactiveMailboxService(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasInactiveMailboxService', []);

        return parent::hasInactiveMailboxService();
    }

    /**
     * {@inheritDoc}
     */
    public function hasMailBoxService(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasMailBoxService', []);

        return parent::hasMailBoxService();
    }

    /**
     * {@inheritDoc}
     */
    public function __call($name, $args)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, '__call', [$name, $args]);

        return parent::__call($name, $args);
    }

    /**
     * {@inheritDoc}
     */
    public function __unset($name)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, '__unset', [$name]);

        return parent::__unset($name);
    }

}
