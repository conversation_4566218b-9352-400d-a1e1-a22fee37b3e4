<?php

namespace CMS\Proxy\__CG__\Entities;


/**
 * DO NOT EDIT THIS FILE - IT WAS CREATED BY DOCTRINE'S PROXY GENERATOR
 */
class Customer extends \Entities\Customer implements \Doctrine\ORM\Proxy\Proxy
{
    /**
     * @var \Closure the callback responsible for loading properties in the proxy object. This callback is called with
     *      three parameters, being respectively the proxy object to be initialized, the method that triggered the
     *      initialization process and an array of ordered parameters that were passed to that method.
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setInitializer
     */
    public $__initializer__;

    /**
     * @var \Closure the callback responsible of loading properties that need to be copied in the cloned object
     *
     * @see \Doctrine\Common\Proxy\Proxy::__setCloner
     */
    public $__cloner__;

    /**
     * @var boolean flag indicating if this object was already initialized
     *
     * @see \Doctrine\Persistence\Proxy::__isInitialized
     */
    public $__isInitialized__ = false;

    /**
     * @var array<string, null> properties to be lazy loaded, indexed by property name
     */
    public static $lazyPropertiesNames = array (
);

    /**
     * @var array<string, mixed> default values of properties to be lazy loaded, with keys being the property names
     *
     * @see \Doctrine\Common\Proxy\Proxy::__getLazyProperties
     */
    public static $lazyPropertiesDefaults = array (
);



    public function __construct(?\Closure $initializer = null, ?\Closure $cloner = null)
    {

        $this->__initializer__ = $initializer;
        $this->__cloner__      = $cloner;
    }

    /**
     * {@inheritDoc}
     * @param string $name
     */
    public function & __get($name)
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__get', [$name]);
        return parent::__get($name);
    }

    /**
     * {@inheritDoc}
     * @param string $name
     * @param mixed  $value
     */
    public function __set($name, $value)
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__set', [$name, $value]);
        return parent::__set($name, $value);
    }

    /**
     * {@inheritDoc}
     * @param  string $name
     * @return boolean
     */
    public function __isset($name)
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__isset', [$name]);

        return parent::__isset($name);
    }

    /**
     * 
     * @return array
     */
    public function __sleep()
    {
        if ($this->__isInitialized__) {
            return ['__isInitialized__', '' . "\0" . 'Entities\\Customer' . "\0" . 'customerId', '' . "\0" . 'Entities\\Customer' . "\0" . 'statusId', '' . "\0" . 'Entities\\Customer' . "\0" . 'roleId', '' . "\0" . 'Entities\\Customer' . "\0" . 'tagId', '' . "\0" . 'Entities\\Customer' . "\0" . 'icaewId', '' . "\0" . 'Entities\\Customer' . "\0" . 'myDetailsQuestion', '' . "\0" . 'Entities\\Customer' . "\0" . 'email', '' . "\0" . 'Entities\\Customer' . "\0" . 'password', '' . "\0" . 'Entities\\Customer' . "\0" . 'titleId', '' . "\0" . 'Entities\\Customer' . "\0" . 'firstName', '' . "\0" . 'Entities\\Customer' . "\0" . 'middleName', '' . "\0" . 'Entities\\Customer' . "\0" . 'lastName', '' . "\0" . 'Entities\\Customer' . "\0" . 'companyName', '' . "\0" . 'Entities\\Customer' . "\0" . 'address1', '' . "\0" . 'Entities\\Customer' . "\0" . 'address2', '' . "\0" . 'Entities\\Customer' . "\0" . 'address3', '' . "\0" . 'Entities\\Customer' . "\0" . 'city', '' . "\0" . 'Entities\\Customer' . "\0" . 'county', '' . "\0" . 'Entities\\Customer' . "\0" . 'postcode', '' . "\0" . 'Entities\\Customer' . "\0" . 'countryId', '' . "\0" . 'Entities\\Customer' . "\0" . 'phone', '' . "\0" . 'Entities\\Customer' . "\0" . 'additionalPhone', '' . "\0" . 'Entities\\Customer' . "\0" . 'isSubscribed', '' . "\0" . 'Entities\\Customer' . "\0" . 'howHeardId', '' . "\0" . 'Entities\\Customer' . "\0" . 'industryId', '' . "\0" . 'Entities\\Customer' . "\0" . 'credit', '' . "\0" . 'Entities\\Customer' . "\0" . 'affiliateId', '' . "\0" . 'Entities\\Customer' . "\0" . 'activeCompanies', '' . "\0" . 'Entities\\Customer' . "\0" . 'monitoredActiveCompanies', '' . "\0" . 'Entities\\Customer' . "\0" . 'cashbackType', '' . "\0" . 'Entities\\Customer' . "\0" . 'accountNumber', '' . "\0" . 'Entities\\Customer' . "\0" . 'sortCode', '' . "\0" . 'Entities\\Customer' . "\0" . 'mailboxAttachmentsEnabled', '' . "\0" . 'Entities\\Customer' . "\0" . 'dtc', '' . "\0" . 'Entities\\Customer' . "\0" . 'dtm', '' . "\0" . 'Entities\\Customer' . "\0" . 'cashbacks', '' . "\0" . 'Entities\\Customer' . "\0" . 'companies', '' . "\0" . 'Entities\\Customer' . "\0" . 'orders', '' . "\0" . 'Entities\\Customer' . "\0" . 'tokens', '' . "\0" . 'Entities\\Customer' . "\0" . 'temporaryPassword', '' . "\0" . 'Entities\\Customer' . "\0" . 'invoiceAddress', '' . "\0" . 'Entities\\Customer' . "\0" . 'logs', '' . "\0" . 'Entities\\Customer' . "\0" . 'dateOfBirth', '' . "\0" . 'Entities\\Customer' . "\0" . 'businessInformation', '' . "\0" . 'Entities\\Customer' . "\0" . 'deleted', '' . "\0" . 'Entities\\Customer' . "\0" . 'events', '' . "\0" . 'Entities\\Customer' . "\0" . 'settings', '' . "\0" . 'Entities\\Customer' . "\0" . 'partOfCredas', '' . "\0" . 'Entities\\Customer' . "\0" . 'isPartOfTheIdCheck', '' . "\0" . 'Entities\\Customer' . "\0" . 'isIdCheckRequired', '' . "\0" . 'Entities\\Customer' . "\0" . 'migrated'];
        }

        return ['__isInitialized__', '' . "\0" . 'Entities\\Customer' . "\0" . 'customerId', '' . "\0" . 'Entities\\Customer' . "\0" . 'statusId', '' . "\0" . 'Entities\\Customer' . "\0" . 'roleId', '' . "\0" . 'Entities\\Customer' . "\0" . 'tagId', '' . "\0" . 'Entities\\Customer' . "\0" . 'icaewId', '' . "\0" . 'Entities\\Customer' . "\0" . 'myDetailsQuestion', '' . "\0" . 'Entities\\Customer' . "\0" . 'email', '' . "\0" . 'Entities\\Customer' . "\0" . 'password', '' . "\0" . 'Entities\\Customer' . "\0" . 'titleId', '' . "\0" . 'Entities\\Customer' . "\0" . 'firstName', '' . "\0" . 'Entities\\Customer' . "\0" . 'middleName', '' . "\0" . 'Entities\\Customer' . "\0" . 'lastName', '' . "\0" . 'Entities\\Customer' . "\0" . 'companyName', '' . "\0" . 'Entities\\Customer' . "\0" . 'address1', '' . "\0" . 'Entities\\Customer' . "\0" . 'address2', '' . "\0" . 'Entities\\Customer' . "\0" . 'address3', '' . "\0" . 'Entities\\Customer' . "\0" . 'city', '' . "\0" . 'Entities\\Customer' . "\0" . 'county', '' . "\0" . 'Entities\\Customer' . "\0" . 'postcode', '' . "\0" . 'Entities\\Customer' . "\0" . 'countryId', '' . "\0" . 'Entities\\Customer' . "\0" . 'phone', '' . "\0" . 'Entities\\Customer' . "\0" . 'additionalPhone', '' . "\0" . 'Entities\\Customer' . "\0" . 'isSubscribed', '' . "\0" . 'Entities\\Customer' . "\0" . 'howHeardId', '' . "\0" . 'Entities\\Customer' . "\0" . 'industryId', '' . "\0" . 'Entities\\Customer' . "\0" . 'credit', '' . "\0" . 'Entities\\Customer' . "\0" . 'affiliateId', '' . "\0" . 'Entities\\Customer' . "\0" . 'activeCompanies', '' . "\0" . 'Entities\\Customer' . "\0" . 'monitoredActiveCompanies', '' . "\0" . 'Entities\\Customer' . "\0" . 'cashbackType', '' . "\0" . 'Entities\\Customer' . "\0" . 'accountNumber', '' . "\0" . 'Entities\\Customer' . "\0" . 'sortCode', '' . "\0" . 'Entities\\Customer' . "\0" . 'mailboxAttachmentsEnabled', '' . "\0" . 'Entities\\Customer' . "\0" . 'dtc', '' . "\0" . 'Entities\\Customer' . "\0" . 'dtm', '' . "\0" . 'Entities\\Customer' . "\0" . 'cashbacks', '' . "\0" . 'Entities\\Customer' . "\0" . 'companies', '' . "\0" . 'Entities\\Customer' . "\0" . 'orders', '' . "\0" . 'Entities\\Customer' . "\0" . 'tokens', '' . "\0" . 'Entities\\Customer' . "\0" . 'temporaryPassword', '' . "\0" . 'Entities\\Customer' . "\0" . 'invoiceAddress', '' . "\0" . 'Entities\\Customer' . "\0" . 'logs', '' . "\0" . 'Entities\\Customer' . "\0" . 'dateOfBirth', '' . "\0" . 'Entities\\Customer' . "\0" . 'businessInformation', '' . "\0" . 'Entities\\Customer' . "\0" . 'deleted', '' . "\0" . 'Entities\\Customer' . "\0" . 'events', '' . "\0" . 'Entities\\Customer' . "\0" . 'settings', '' . "\0" . 'Entities\\Customer' . "\0" . 'partOfCredas', '' . "\0" . 'Entities\\Customer' . "\0" . 'isPartOfTheIdCheck', '' . "\0" . 'Entities\\Customer' . "\0" . 'isIdCheckRequired', '' . "\0" . 'Entities\\Customer' . "\0" . 'migrated'];
    }

    /**
     * 
     */
    public function __wakeup()
    {
        if ( ! $this->__isInitialized__) {
            $this->__initializer__ = function (Customer $proxy) {
                $proxy->__setInitializer(null);
                $proxy->__setCloner(null);

                $existingProperties = get_object_vars($proxy);

                foreach ($proxy::$lazyPropertiesDefaults as $property => $defaultValue) {
                    if ( ! array_key_exists($property, $existingProperties)) {
                        $proxy->$property = $defaultValue;
                    }
                }
            };

        }
    }

    /**
     * 
     */
    public function __clone()
    {
        $this->__cloner__ && $this->__cloner__->__invoke($this, '__clone', []);
    }

    /**
     * Forces initialization of the proxy
     */
    public function __load(): void
    {
        $this->__initializer__ && $this->__initializer__->__invoke($this, '__load', []);
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __isInitialized(): bool
    {
        return $this->__isInitialized__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitialized($initialized): void
    {
        $this->__isInitialized__ = $initialized;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setInitializer(\Closure $initializer = null): void
    {
        $this->__initializer__ = $initializer;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __getInitializer(): ?\Closure
    {
        return $this->__initializer__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     */
    public function __setCloner(\Closure $cloner = null): void
    {
        $this->__cloner__ = $cloner;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific cloning logic
     */
    public function __getCloner(): ?\Closure
    {
        return $this->__cloner__;
    }

    /**
     * {@inheritDoc}
     * @internal generated method: use only when explicitly handling proxy specific loading logic
     * @deprecated no longer in use - generated code now relies on internal components rather than generated public API
     * @static
     */
    public function __getLazyProperties(): array
    {
        return self::$lazyPropertiesDefaults;
    }

    
    /**
     * {@inheritDoc}
     */
    public function getId()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getId', []);

        return parent::getId();
    }

    /**
     * {@inheritDoc}
     */
    public function getCustomerId()
    {
        if ($this->__isInitialized__ === false) {
            return (int)  parent::getCustomerId();
        }


        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCustomerId', []);

        return parent::getCustomerId();
    }

    /**
     * {@inheritDoc}
     */
    public function hasId(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasId', []);

        return parent::hasId();
    }

    /**
     * {@inheritDoc}
     */
    public function getStatusId()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getStatusId', []);

        return parent::getStatusId();
    }

    /**
     * {@inheritDoc}
     */
    public function getRoleId()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getRoleId', []);

        return parent::getRoleId();
    }

    /**
     * {@inheritDoc}
     */
    public function getTagId()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTagId', []);

        return parent::getTagId();
    }

    /**
     * {@inheritDoc}
     */
    public function getIcaewId()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getIcaewId', []);

        return parent::getIcaewId();
    }

    /**
     * {@inheritDoc}
     */
    public function getMyDetailsQuestion()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getMyDetailsQuestion', []);

        return parent::getMyDetailsQuestion();
    }

    /**
     * {@inheritDoc}
     */
    public function getEmail()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getEmail', []);

        return parent::getEmail();
    }

    /**
     * {@inheritDoc}
     */
    public function getPassword()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPassword', []);

        return parent::getPassword();
    }

    /**
     * {@inheritDoc}
     */
    public function getTitleId()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTitleId', []);

        return parent::getTitleId();
    }

    /**
     * {@inheritDoc}
     */
    public function getFirstName()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getFirstName', []);

        return parent::getFirstName();
    }

    /**
     * {@inheritDoc}
     */
    public function getMiddleName()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getMiddleName', []);

        return parent::getMiddleName();
    }

    /**
     * {@inheritDoc}
     */
    public function setMiddleName($middleName)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setMiddleName', [$middleName]);

        return parent::setMiddleName($middleName);
    }

    /**
     * {@inheritDoc}
     */
    public function getLastName()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLastName', []);

        return parent::getLastName();
    }

    /**
     * {@inheritDoc}
     */
    public function getCompanyName()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCompanyName', []);

        return parent::getCompanyName();
    }

    /**
     * {@inheritDoc}
     */
    public function getFullName()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getFullName', []);

        return parent::getFullName();
    }

    /**
     * {@inheritDoc}
     */
    public function getAddress1()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAddress1', []);

        return parent::getAddress1();
    }

    /**
     * {@inheritDoc}
     */
    public function getAddress2()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAddress2', []);

        return parent::getAddress2();
    }

    /**
     * {@inheritDoc}
     */
    public function getAddress3()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAddress3', []);

        return parent::getAddress3();
    }

    /**
     * {@inheritDoc}
     */
    public function getCity()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCity', []);

        return parent::getCity();
    }

    /**
     * {@inheritDoc}
     */
    public function getCounty()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCounty', []);

        return parent::getCounty();
    }

    /**
     * {@inheritDoc}
     */
    public function getPostcode()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPostcode', []);

        return parent::getPostcode();
    }

    /**
     * {@inheritDoc}
     */
    public function getCountryId()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCountryId', []);

        return parent::getCountryId();
    }

    /**
     * {@inheritDoc}
     */
    public function getPhone()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPhone', []);

        return parent::getPhone();
    }

    /**
     * {@inheritDoc}
     */
    public function getAdditionalPhone()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAdditionalPhone', []);

        return parent::getAdditionalPhone();
    }

    /**
     * {@inheritDoc}
     */
    public function isSubscribed()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isSubscribed', []);

        return parent::isSubscribed();
    }

    /**
     * {@inheritDoc}
     */
    public function getHowHeardId()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getHowHeardId', []);

        return parent::getHowHeardId();
    }

    /**
     * {@inheritDoc}
     */
    public function getIndustryId()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getIndustryId', []);

        return parent::getIndustryId();
    }

    /**
     * {@inheritDoc}
     */
    public function getCredit()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCredit', []);

        return parent::getCredit();
    }

    /**
     * {@inheritDoc}
     */
    public function getAffiliateId()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAffiliateId', []);

        return parent::getAffiliateId();
    }

    /**
     * {@inheritDoc}
     */
    public function getActiveCompanies()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getActiveCompanies', []);

        return parent::getActiveCompanies();
    }

    /**
     * {@inheritDoc}
     */
    public function getMonitoredActiveCompanies()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getMonitoredActiveCompanies', []);

        return parent::getMonitoredActiveCompanies();
    }

    /**
     * {@inheritDoc}
     */
    public function getCashbackType()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCashbackType', []);

        return parent::getCashbackType();
    }

    /**
     * {@inheritDoc}
     */
    public function getAccountNumber()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAccountNumber', []);

        return parent::getAccountNumber();
    }

    /**
     * {@inheritDoc}
     */
    public function getSortCode()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSortCode', []);

        return parent::getSortCode();
    }

    /**
     * {@inheritDoc}
     */
    public function getDtc()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDtc', []);

        return parent::getDtc();
    }

    /**
     * {@inheritDoc}
     */
    public function getDtm()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDtm', []);

        return parent::getDtm();
    }

    /**
     * {@inheritDoc}
     */
    public function setCustomerId($customerId)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCustomerId', [$customerId]);

        return parent::setCustomerId($customerId);
    }

    /**
     * {@inheritDoc}
     */
    public function setStatusId($statusId)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setStatusId', [$statusId]);

        return parent::setStatusId($statusId);
    }

    /**
     * {@inheritDoc}
     */
    public function setRoleId($roleId)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setRoleId', [$roleId]);

        return parent::setRoleId($roleId);
    }

    /**
     * {@inheritDoc}
     */
    public function setTagId($tagId)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setTagId', [$tagId]);

        return parent::setTagId($tagId);
    }

    /**
     * {@inheritDoc}
     */
    public function setIcaewId($icaewId)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setIcaewId', [$icaewId]);

        return parent::setIcaewId($icaewId);
    }

    /**
     * {@inheritDoc}
     */
    public function setMyDetailsQuestion($myDetailsQuestion)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setMyDetailsQuestion', [$myDetailsQuestion]);

        return parent::setMyDetailsQuestion($myDetailsQuestion);
    }

    /**
     * {@inheritDoc}
     */
    public function setEmail($email)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setEmail', [$email]);

        return parent::setEmail($email);
    }

    /**
     * {@inheritDoc}
     */
    public function setPassword($password)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setPassword', [$password]);

        return parent::setPassword($password);
    }

    /**
     * {@inheritDoc}
     */
    public function setTitleId($titleId)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setTitleId', [$titleId]);

        return parent::setTitleId($titleId);
    }

    /**
     * {@inheritDoc}
     */
    public function setFirstName($firstName)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setFirstName', [$firstName]);

        return parent::setFirstName($firstName);
    }

    /**
     * {@inheritDoc}
     */
    public function setLastName($lastName)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setLastName', [$lastName]);

        return parent::setLastName($lastName);
    }

    /**
     * {@inheritDoc}
     */
    public function setCompanyName($companyName)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCompanyName', [$companyName]);

        return parent::setCompanyName($companyName);
    }

    /**
     * {@inheritDoc}
     */
    public function getRecipientName()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getRecipientName', []);

        return parent::getRecipientName();
    }

    /**
     * {@inheritDoc}
     */
    public function setRecipientName($recipientName)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setRecipientName', [$recipientName]);

        return parent::setRecipientName($recipientName);
    }

    /**
     * {@inheritDoc}
     */
    public function setAddress1($address1)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setAddress1', [$address1]);

        return parent::setAddress1($address1);
    }

    /**
     * {@inheritDoc}
     */
    public function setAddress2($address2)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setAddress2', [$address2]);

        return parent::setAddress2($address2);
    }

    /**
     * {@inheritDoc}
     */
    public function setCity($city)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCity', [$city]);

        return parent::setCity($city);
    }

    /**
     * {@inheritDoc}
     */
    public function setPostcode($postcode)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setPostcode', [$postcode]);

        return parent::setPostcode($postcode);
    }

    /**
     * {@inheritDoc}
     */
    public function setCountryId($countryId)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCountryId', [$countryId]);

        return parent::setCountryId($countryId);
    }

    /**
     * {@inheritDoc}
     */
    public function setPhone($phone)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setPhone', [$phone]);

        return parent::setPhone($phone);
    }

    /**
     * {@inheritDoc}
     */
    public function setIsSubscribed($isSubscribed)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setIsSubscribed', [$isSubscribed]);

        return parent::setIsSubscribed($isSubscribed);
    }

    /**
     * {@inheritDoc}
     */
    public function setHowHeardId($howHeardId)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setHowHeardId', [$howHeardId]);

        return parent::setHowHeardId($howHeardId);
    }

    /**
     * {@inheritDoc}
     */
    public function setIndustryId($industryId)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setIndustryId', [$industryId]);

        return parent::setIndustryId($industryId);
    }

    /**
     * {@inheritDoc}
     */
    public function setCredit($credit)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCredit', [$credit]);

        return parent::setCredit($credit);
    }

    /**
     * {@inheritDoc}
     */
    public function setAffiliateId($affiliateId)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setAffiliateId', [$affiliateId]);

        return parent::setAffiliateId($affiliateId);
    }

    /**
     * {@inheritDoc}
     */
    public function setActiveCompanies($activeCompanies)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setActiveCompanies', [$activeCompanies]);

        return parent::setActiveCompanies($activeCompanies);
    }

    /**
     * {@inheritDoc}
     */
    public function setMonitoredActiveCompanies($monitoredActiveCompanies)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setMonitoredActiveCompanies', [$monitoredActiveCompanies]);

        return parent::setMonitoredActiveCompanies($monitoredActiveCompanies);
    }

    /**
     * {@inheritDoc}
     */
    public function setCashbackType($cashbackType)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCashbackType', [$cashbackType]);

        return parent::setCashbackType($cashbackType);
    }

    /**
     * {@inheritDoc}
     */
    public function setAccountNumber($accountNumber)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setAccountNumber', [$accountNumber]);

        return parent::setAccountNumber($accountNumber);
    }

    /**
     * {@inheritDoc}
     */
    public function setSortCode($sortCode)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setSortCode', [$sortCode]);

        return parent::setSortCode($sortCode);
    }

    /**
     * {@inheritDoc}
     */
    public function setDtc(\DateTime $dtc)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDtc', [$dtc]);

        return parent::setDtc($dtc);
    }

    /**
     * {@inheritDoc}
     */
    public function setDtm(\DateTime $dtm)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDtm', [$dtm]);

        return parent::setDtm($dtm);
    }

    /**
     * {@inheritDoc}
     */
    public function isRetail()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isRetail', []);

        return parent::isRetail();
    }

    /**
     * {@inheritDoc}
     */
    public function toArray()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'toArray', []);

        return parent::toArray();
    }

    /**
     * {@inheritDoc}
     */
    public function isEqual(\UserModule\Contracts\ICustomer $customer)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isEqual', [$customer]);

        return parent::isEqual($customer);
    }

    /**
     * {@inheritDoc}
     */
    public function getAddress($newLine = '
')
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getAddress', [$newLine]);

        return parent::getAddress($newLine);
    }

    /**
     * {@inheritDoc}
     */
    public function hasBankDetails()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasBankDetails', []);

        return parent::hasBankDetails();
    }

    /**
     * {@inheritDoc}
     */
    public function hasCompletedRegistration()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasCompletedRegistration', []);

        return parent::hasCompletedRegistration();
    }

    /**
     * {@inheritDoc}
     */
    public function hasCashbackTypeBank()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasCashbackTypeBank', []);

        return parent::hasCashbackTypeBank();
    }

    /**
     * {@inheritDoc}
     */
    public function isWholesale()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isWholesale', []);

        return parent::isWholesale();
    }

    /**
     * {@inheritDoc}
     */
    public function getCompanies()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCompanies', []);

        return parent::getCompanies();
    }

    /**
     * {@inheritDoc}
     */
    public function getTokens()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTokens', []);

        return parent::getTokens();
    }

    /**
     * {@inheritDoc}
     */
    public function hasCashbackTypeCredits()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasCashbackTypeCredits', []);

        return parent::hasCashbackTypeCredits();
    }

    /**
     * {@inheritDoc}
     */
    public function hasCompanies()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasCompanies', []);

        return parent::hasCompanies();
    }

    /**
     * {@inheritDoc}
     */
    public function hasWholesaleQuestion()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasWholesaleQuestion', []);

        return parent::hasWholesaleQuestion();
    }

    /**
     * {@inheritDoc}
     */
    public function addCompany(\Entities\Company $company)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'addCompany', [$company]);

        return parent::addCompany($company);
    }

    /**
     * {@inheritDoc}
     */
    public function hasActiveToken()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasActiveToken', []);

        return parent::hasActiveToken();
    }

    /**
     * {@inheritDoc}
     */
    public function hasBackupToken(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasBackupToken', []);

        return parent::hasBackupToken();
    }

    /**
     * {@inheritDoc}
     */
    public function getActiveToken(bool $isCurrent = true): ?\Entities\Payment\Token
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getActiveToken', [$isCurrent]);

        return parent::getActiveToken($isCurrent);
    }

    /**
     * {@inheritDoc}
     */
    public function addToken(\Entities\Payment\Token $token)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'addToken', [$token]);

        return parent::addToken($token);
    }

    /**
     * {@inheritDoc}
     */
    public function isNew()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isNew', []);

        return parent::isNew();
    }

    /**
     * {@inheritDoc}
     */
    public function addCredit($credit)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'addCredit', [$credit]);

        return parent::addCredit($credit);
    }

    /**
     * {@inheritDoc}
     */
    public function getCountry2Letter()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCountry2Letter', []);

        return parent::getCountry2Letter();
    }

    /**
     * {@inheritDoc}
     */
    public function setCountryFromString($country)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCountryFromString', [$country]);

        return parent::setCountryFromString($country);
    }

    /**
     * {@inheritDoc}
     */
    public function getCountry()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCountry', []);

        return parent::getCountry();
    }

    /**
     * {@inheritDoc}
     */
    public function isUkCustomer()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isUkCustomer', []);

        return parent::isUkCustomer();
    }

    /**
     * {@inheritDoc}
     */
    public function getOrders()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getOrders', []);

        return parent::getOrders();
    }

    /**
     * {@inheritDoc}
     */
    public function getRole()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getRole', []);

        return parent::getRole();
    }

    /**
     * {@inheritDoc}
     */
    public function getTitle()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTitle', []);

        return parent::getTitle();
    }

    /**
     * {@inheritDoc}
     */
    public function getPrimaryAddress()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPrimaryAddress', []);

        return parent::getPrimaryAddress();
    }

    /**
     * {@inheritDoc}
     */
    public function hasEnoughCredit($credit)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasEnoughCredit', [$credit]);

        return parent::hasEnoughCredit($credit);
    }

    /**
     * {@inheritDoc}
     */
    public function subtractCredit($credit)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'subtractCredit', [$credit]);

        return parent::subtractCredit($credit);
    }

    /**
     * {@inheritDoc}
     */
    public function hasCredit()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasCredit', []);

        return parent::hasCredit();
    }

    /**
     * {@inheritDoc}
     */
    public function setCountryIso($country)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setCountryIso', [$country]);

        return parent::setCountryIso($country);
    }

    /**
     * {@inheritDoc}
     */
    public function getCountryIso()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCountryIso', []);

        return parent::getCountryIso();
    }

    /**
     * {@inheritDoc}
     */
    public function jsonSerialize(): mixed
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'jsonSerialize', []);

        return parent::jsonSerialize();
    }

    /**
     * {@inheritDoc}
     */
    public function setTemporaryPassword($password)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setTemporaryPassword', [$password]);

        return parent::setTemporaryPassword($password);
    }

    /**
     * {@inheritDoc}
     */
    public function getTemporaryPassword()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getTemporaryPassword', []);

        return parent::getTemporaryPassword();
    }

    /**
     * {@inheritDoc}
     */
    public function setInvoiceAddress(\UserModule\Contracts\IAddress $address)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setInvoiceAddress', [$address]);

        return parent::setInvoiceAddress($address);
    }

    /**
     * {@inheritDoc}
     */
    public function getInvoiceAddress()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getInvoiceAddress', []);

        return parent::getInvoiceAddress();
    }

    /**
     * {@inheritDoc}
     */
    public function hasInvoiceAddress()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasInvoiceAddress', []);

        return parent::hasInvoiceAddress();
    }

    /**
     * {@inheritDoc}
     */
    public function removeInvoiceAddress()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'removeInvoiceAddress', []);

        return parent::removeInvoiceAddress();
    }

    /**
     * {@inheritDoc}
     */
    public function setPrimaryAddress(\UserModule\Contracts\IAddress $address)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setPrimaryAddress', [$address]);

        return parent::setPrimaryAddress($address);
    }

    /**
     * {@inheritDoc}
     */
    public function getCashBackPreferenceCreationDate()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCashBackPreferenceCreationDate', []);

        return parent::getCashBackPreferenceCreationDate();
    }

    /**
     * {@inheritDoc}
     */
    public function getLatestCashBackPreferenceUpdateDate()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLatestCashBackPreferenceUpdateDate', []);

        return parent::getLatestCashBackPreferenceUpdateDate();
    }

    /**
     * {@inheritDoc}
     */
    public function addLog(\Entities\CustomerLog $log)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'addLog', [$log]);

        return parent::addLog($log);
    }

    /**
     * {@inheritDoc}
     */
    public function getDateOfBirth(): ?\Utils\Date
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getDateOfBirth', []);

        return parent::getDateOfBirth();
    }

    /**
     * {@inheritDoc}
     */
    public function hasDateOfBirth(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasDateOfBirth', []);

        return parent::hasDateOfBirth();
    }

    /**
     * {@inheritDoc}
     */
    public function setDateOfBirth(\Utils\Date $dateOfBirth)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setDateOfBirth', [$dateOfBirth]);

        return parent::setDateOfBirth($dateOfBirth);
    }

    /**
     * {@inheritDoc}
     */
    public function hasMailboxAttachmentsEnabled(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasMailboxAttachmentsEnabled', []);

        return parent::hasMailboxAttachmentsEnabled();
    }

    /**
     * {@inheritDoc}
     */
    public function getMailboxAttachmentsEnabled(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getMailboxAttachmentsEnabled', []);

        return parent::getMailboxAttachmentsEnabled();
    }

    /**
     * {@inheritDoc}
     */
    public function setMailboxAttachmentsEnabled(bool $mailboxAttachmentsEnabled): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setMailboxAttachmentsEnabled', [$mailboxAttachmentsEnabled]);

        parent::setMailboxAttachmentsEnabled($mailboxAttachmentsEnabled);
    }

    /**
     * {@inheritDoc}
     */
    public function getBusinessInformation(): ?\CustomerModule\Entities\BusinessInformation
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getBusinessInformation', []);

        return parent::getBusinessInformation();
    }

    /**
     * {@inheritDoc}
     */
    public function setBusinessInformation(?\CustomerModule\Entities\BusinessInformation $businessInformation): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setBusinessInformation', [$businessInformation]);

        parent::setBusinessInformation($businessInformation);
    }

    /**
     * {@inheritDoc}
     */
    public function getBusinessType(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getBusinessType', []);

        return parent::getBusinessType();
    }

    /**
     * {@inheritDoc}
     */
    public function hasBusinessTypeRequired(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasBusinessTypeRequired', []);

        return parent::hasBusinessTypeRequired();
    }

    /**
     * {@inheritDoc}
     */
    public function isDeleted(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isDeleted', []);

        return parent::isDeleted();
    }

    /**
     * {@inheritDoc}
     */
    public function markAsDeleted()
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'markAsDeleted', []);

        return parent::markAsDeleted();
    }

    /**
     * {@inheritDoc}
     */
    public function getLastDeletedBy(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLastDeletedBy', []);

        return parent::getLastDeletedBy();
    }

    /**
     * {@inheritDoc}
     */
    public function getLastDeletedTime(): ?\DateTimeInterface
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getLastDeletedTime', []);

        return parent::getLastDeletedTime();
    }

    /**
     * {@inheritDoc}
     */
    public function hasCountry(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasCountry', []);

        return parent::hasCountry();
    }

    /**
     * {@inheritDoc}
     */
    public function isPartOfRegulatedBody(): ?bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isPartOfRegulatedBody', []);

        return parent::isPartOfRegulatedBody();
    }

    /**
     * {@inheritDoc}
     */
    public function getRegulationNumber(): ?string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getRegulationNumber', []);

        return parent::getRegulationNumber();
    }

    /**
     * {@inheritDoc}
     */
    public function isCustomerFirstCompany(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isCustomerFirstCompany', []);

        return parent::isCustomerFirstCompany();
    }

    /**
     * {@inheritDoc}
     */
    public function markAsCredas(): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'markAsCredas', []);

        parent::markAsCredas();
    }

    /**
     * {@inheritDoc}
     */
    public function isPartOfCredas(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isPartOfCredas', []);

        return parent::isPartOfCredas();
    }

    /**
     * {@inheritDoc}
     */
    public function isIdCheckRequired(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isIdCheckRequired', []);

        return parent::isIdCheckRequired();
    }

    /**
     * {@inheritDoc}
     */
    public function setIdCheckRequired(bool $isIdCheckRequired): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setIdCheckRequired', [$isIdCheckRequired]);

        parent::setIdCheckRequired($isIdCheckRequired);
    }

    /**
     * {@inheritDoc}
     */
    public function isPartOfTheIdCheck(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isPartOfTheIdCheck', []);

        return parent::isPartOfTheIdCheck();
    }

    /**
     * {@inheritDoc}
     */
    public function setPartOfTheIdCheck(bool $isPartOfTheIdCheck): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setPartOfTheIdCheck', [$isPartOfTheIdCheck]);

        parent::setPartOfTheIdCheck($isPartOfTheIdCheck);
    }

    /**
     * {@inheritDoc}
     */
    public function setPartOfCredas(bool $partOfCredas): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setPartOfCredas', [$partOfCredas]);

        parent::setPartOfCredas($partOfCredas);
    }

    /**
     * {@inheritDoc}
     */
    public function addSetting(\CustomerModule\Entities\CustomerSetting $setting): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'addSetting', [$setting]);

        parent::addSetting($setting);
    }

    /**
     * {@inheritDoc}
     */
    public function getSettings(): array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSettings', []);

        return parent::getSettings();
    }

    /**
     * {@inheritDoc}
     */
    public function hasSettings(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasSettings', []);

        return parent::hasSettings();
    }

    /**
     * {@inheritDoc}
     */
    public function getSetting(string $className): ?\CustomerModule\Entities\CustomerSetting
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getSetting', [$className]);

        return parent::getSetting($className);
    }

    /**
     * {@inheritDoc}
     */
    public function isOmnipay(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isOmnipay', []);

        return parent::isOmnipay();
    }

    /**
     * {@inheritDoc}
     */
    public function getPaymentGateway(bool $capitalize = false): string
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getPaymentGateway', [$capitalize]);

        return parent::getPaymentGateway($capitalize);
    }

    /**
     * {@inheritDoc}
     */
    public function isOptionalRenewalProductAvailable($productId): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isOptionalRenewalProductAvailable', [$productId]);

        return parent::isOptionalRenewalProductAvailable($productId);
    }

    /**
     * {@inheritDoc}
     */
    public function hasCompanyWithActiveService(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasCompanyWithActiveService', []);

        return parent::hasCompanyWithActiveService();
    }

    /**
     * {@inheritDoc}
     */
    public function hasEmptyPassword(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'hasEmptyPassword', []);

        return parent::hasEmptyPassword();
    }

    /**
     * {@inheritDoc}
     */
    public function setMigrated(bool $isMigrated): void
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'setMigrated', [$isMigrated]);

        parent::setMigrated($isMigrated);
    }

    /**
     * {@inheritDoc}
     */
    public function isMigrated(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isMigrated', []);

        return parent::isMigrated();
    }

    /**
     * {@inheritDoc}
     */
    public function isOnNewIncorporationProcess(): bool
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'isOnNewIncorporationProcess', []);

        return parent::isOnNewIncorporationProcess();
    }

    /**
     * {@inheritDoc}
     */
    public function getCompanyNumbers(): ?array
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, 'getCompanyNumbers', []);

        return parent::getCompanyNumbers();
    }

    /**
     * {@inheritDoc}
     */
    public function __call($name, $args)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, '__call', [$name, $args]);

        return parent::__call($name, $args);
    }

    /**
     * {@inheritDoc}
     */
    public function __unset($name)
    {

        $this->__initializer__ && $this->__initializer__->__invoke($this, '__unset', [$name]);

        return parent::__unset($name);
    }

}
