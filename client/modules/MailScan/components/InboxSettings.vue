<template>
    <div class="bg-white" style="border-bottom: 2px solid #ddd">
        <div class="container">
            <p><a href="/my-company-inbox/">My company inbox</a> &gt; Settings</p>

            <div class="row">
                <div class="col-xs-12">
                    <h1>Company Inbox Settings</h1>
                </div>
            </div>

            <div>
                <div class="alert alert-success" v-if="successMessage">
                    {{ successMessage }}
                </div>
                <div class="alert alert-danger" v-if="this.companiesWithInvalidAddress().length > 0">
                    If Post option is selected, please provide the address you would like us to forward your items to. These are the companies that need an address:
                    <ul>
                        <li v-for="setting in companiesSettings" :key="setting.id" v-if="setting.needValidAddress === true">{{ setting.name }}</li>
                    </ul>
                </div>
            </div>

            <div class="row">
                <div class="col-xs-12">
                    <div class="border p-4">
                        <h3 class="mb-1">Attachments</h3>
                        <span>We will email you whenever we scan an item of your company's post.</span>

                        <div class="mt10">
                            <label>
                                <input type="radio" name="attachments" :value="true"
                                       v-model="customerSettings.mailboxAttachmentsEnabled" />
                                Attach my post to my email notifications
                            </label>
                            <br />
                            <label>
                                <input type="radio" name="attachments" :value="false"
                                       v-model="customerSettings.mailboxAttachmentsEnabled" />
                                Do not attach my post to my email notifications. (I'll log in each time to access my
                                post)
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <div>
                <div class="row mt-2" v-if="Object.keys(initialCompaniesSettings).length > 0">
                    <div class="col-xs-12">
                        <div class="border p-4">
                            <div class="row">
                                <div class="col-xs-12">
                                    <h3>Choose the company:</h3>
                                    <div class="custom-select">
                                        <div class="select-header" @click="toggleDropdown">
                                            {{ selectedCompanySettings.name || 'Select a company' }}
                                            <span v-if="selectedCompanySettings.needValidAddress" class="address-warning">
                                                <div style="display: flex; align-items: center; gap: 8px;">
                                                  <div>⚠</div>
                                                  <div>Address issues</div>
                                                </div>
                                            </span>
                                            <span class="arrow" :class="{ 'open': dropdownOpen }">▼</span>
                                        </div>
                                        <div class="select-options" v-show="dropdownOpen">
                                            <div
                                                v-for="(companyInfo, companyId) in companiesSettings"
                                                :key="companyId"
                                                class="select-option"
                                                :class="{ 'needs-address': companyInfo.needValidAddress, 'selected': selectedCompanyId === companyId }"
                                                @click="selectCompany(companyId)"
                                            >
                                                {{ companyInfo.name }}
                                                <span v-if="companyInfo.needValidAddress" class="address-warning">⚠ Address issues</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div v-if="selectedCompanySettings.mailboxTier" class="row mt-3 col-xs-12">
                                <div v-if="this.getMailSettings()" class="row mt-3 col-xs-12">
                                    <h3 class="mb-1">Mail Settings</h3>
                                    <div class="row">
                                        <p class="mb-0">
                                            <span>Choose how you want to handle your post-items with three convenient options
                                                tailored to your needs.</span>
                                        </p>
                                        <div>
                                            <label v-if="this.getMailSettings().includes(POST_ITEM_SCAN_ONLY)">
                                                <input type="radio" name="post-items-settings" :value="POST_ITEM_SCAN_ONLY"
                                                       v-model="selectedCompanySettings.postItemsSettings" />
                                                Scan Only
                                            </label>
                                            <br />
                                            <label :class="{ 'fade-label': !this.getMailSettings().includes(POST_ITEM_SCAN_AND_COLLECT) }">
                                                <input type="radio" name="post-items-settings" :value="POST_ITEM_SCAN_AND_COLLECT"
                                                       v-model="selectedCompanySettings.postItemsSettings" :disabled="!this.getMailSettings().includes(POST_ITEM_SCAN_AND_COLLECT)" />
                                                Scan & Collect
                                                <span v-if="!this.getMailSettings().includes(POST_ITEM_SCAN_AND_COLLECT)" class="mailbox-cta-text">
                                                    (access to this option requires a <a :href="`/services/registered-office-address/#matrix-table`">Mailbox upgrade</a>.)
                                                </span>
                                            </label>
                                            <br />
                                            <label :class="{ 'fade-label': !this.getMailSettings().includes(POST_ITEM_SCAN_AND_POST) }">
                                                <input type="radio" name="post-items-settings" :value="POST_ITEM_SCAN_AND_POST"
                                                       v-model="selectedCompanySettings.postItemsSettings" :disabled="!this.getMailSettings().includes(POST_ITEM_SCAN_AND_POST)" />
                                                Scan & Post
                                                <span v-if="!this.getMailSettings().includes(POST_ITEM_SCAN_AND_POST)" class="mailbox-cta-text">
                                                    (access to this option requires a <a :href="`/services/registered-office-address/#matrix-table`">Mailbox upgrade</a>.)
                                                </span>
                                            </label>

                                        </div>
                                    </div>
                                </div>

                                <div v-if="this.getParcelSettings()" class="row mt-3 col-xs-12">
                                    <h3 class="mb-1">Parcel Forwarding Settings</h3>
                                    <div class="row">
                                        <p class="mb-0">
                                            <span>Choose how you want to handle your forwarded mail with two convenient options
                                                tailored to your needs.</span>
                                        </p>
                                        <div>
                                            <label :class="{ 'fade-label': !this.getParcelSettings().includes(PARCEL_REJECT) }">
                                                <input type="radio" name="parcels-settings" :value="PARCEL_REJECT" v-model="selectedCompanySettings.parcelsSettings"
                                                       :disabled="!this.getParcelSettings().includes(PARCEL_REJECT)" />
                                                Reject
                                                <span v-if="!this.getParcelSettings().includes(PARCEL_REJECT)" class="mailbox-cta-text">
                                                    (access to this option requires a <a :href="`/services/registered-office-address/#matrix-table`">Mailbox upgrade</a>.)
                                                </span>
                                            </label>
                                            <br />
                                            <label :class="{ 'fade-label': !this.getParcelSettings().includes(PARCEL_COLLECT) }">
                                                <input type="radio" name="parcels-settings" :value="PARCEL_REJECT" v-model="selectedCompanySettings.parcelsSettings"
                                                       :disabled="!this.getParcelSettings().includes(PARCEL_REJECT)" />
                                                Collect
                                                <span v-if="!this.getParcelSettings().includes(PARCEL_REJECT)" class="mailbox-cta-text">
                                                    (access to this option requires a <a :href="`/services/registered-office-address/#matrix-table`">Mailbox upgrade</a>.)
                                                </span>
                                            </label>
                                            <br />
                                            <label :class="{ 'fade-label': !this.getParcelSettings().includes(PARCEL_POST) }">
                                                <input type="radio" name="parcels-settings" :value="PARCEL_POST" v-model="selectedCompanySettings.parcelsSettings"
                                                       :disabled="!this.getParcelSettings().includes(PARCEL_POST)" />
                                                Post
                                                <span v-if="!this.getParcelSettings().includes(PARCEL_POST)" class="mailbox-cta-text">
                                                    (access to this option requires a <a :href="`/services/registered-office-address/#matrix-table`">Mailbox upgrade</a>.)
                                                </span>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div
                                    v-if="selectedCompanySettings.postItemsSettings == POST_ITEM_SCAN_AND_POST ||
                                          selectedCompanySettings.parcelsSettings == PARCEL_POST"
                                    class="row mt-3 col-xs-12"
                                >
                                    <h3 class="mb-1">Mail Forwarding Address</h3>
                                    <div class="form-address col-sm-6">
                                        <input type="text"
                                               v-model="selectedCompanySettings.address1"
                                               placeholder="Building name/number *"
                                               class="form-control mb-2"
                                               required
                                        />
                                        <input type="text"
                                               v-model="selectedCompanySettings.address2"
                                               placeholder="Street *"
                                               class="form-control mb-2"
                                        />
                                        <input type="text"
                                               v-model="selectedCompanySettings.address3"
                                               placeholder="Address Line 3"
                                               class="form-control mb-2"
                                        />
                                        <input type="text" v-model="selectedCompanySettings.town" placeholder="Town *" class="form-control mb-2" />
                                        <input type="text" v-model="selectedCompanySettings.country" placeholder="Country *" class="form-control mb-2" />
                                        <input type="text" v-model="selectedCompanySettings.postcode" placeholder="PostCode *"
                                               class="form-control mb-2" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-danger" v-if="noChangesMessage">
                {{ noChangesMessage }}
            </div>
            <div class="row m-4">
                <button class="btn btn-orange border-0 ps-3 pe-3" @click="saveSettings">Save settings</button>
            </div>

            <div>
                <div class="alert alert-danger" v-if="errorMessage">
                    {{ errorMessage }}
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { defineComponent } from "vue";
import api from "@/services/api";
const POST_ITEM_SCAN_ONLY = 1
const POST_ITEM_SCAN_AND_COLLECT = 2
const POST_ITEM_SCAN_AND_POST = 3
const PARCEL_REJECT = 0
const PARCEL_COLLECT = 1
const PARCEL_POST = 2


export default defineComponent({
    data() {
        return {
            POST_ITEM_SCAN_ONLY,
            POST_ITEM_SCAN_AND_COLLECT,
            POST_ITEM_SCAN_AND_POST,
            PARCEL_REJECT,
            PARCEL_COLLECT,
            PARCEL_POST,

            selectedCompanyId: null,
            errorMessage: null,
            successMessage: null,
            noChangesMessage: null,
            initialCustomerSettings: {},
            initialCompaniesSettings: {},
            workingCompanySettings: {},
            dropdownOpen: false
        };
    },
    props: {
        customerSettings: {
            type: Object,
            required: true,
        },
        companiesSettings: {
            type: Object,
            required: false,
        },
        mailboxConfig: {
            type: Object,
            required: true,
        },
    },
    computed: {
        selectedCompanySettings() {
            if (this.selectedCompanyId && this.workingCompanySettings[this.selectedCompanyId]) {
                return this.workingCompanySettings[this.selectedCompanyId];
            }
            return this.initialCompaniesSettings[this.selectedCompanyId] || {};
        },
    },
    mounted() {
        if (new URLSearchParams(window.location.search).get('success') === 'true') {
            this.successMessage = "Settings have been successfully saved";
        }

        this.initialCompaniesSettings = {...this.companiesSettings};
        this.initialCustomerSettings = {...this.customerSettings};

        const companyIds = Object.keys(this.companiesSettings);
        if (companyIds.length > 0) {
            const urlCompanyId = new URLSearchParams(window.location.search).get('company');

            if (urlCompanyId && companyIds.includes(urlCompanyId)) {
                this.selectedCompanyId = urlCompanyId;
            } else {
                this.selectedCompanyId = this.companiesWithInvalidAddress()[0] || companyIds[0];
            }

            this.$set(this.workingCompanySettings, this.selectedCompanyId, {
                ...this.initialCompaniesSettings[this.selectedCompanyId]
            });
        }
        document.addEventListener('click', this.handleClickOutside);
    },
    beforeUnmount() {
        document.removeEventListener('click', this.handleClickOutside);
    },
    methods: {
        toggleDropdown() {
            this.dropdownOpen = !this.dropdownOpen;
        },
        selectCompany(companyId) {
            // Save current changes back to companiesSettings before switching
            if (this.selectedCompanyId && this.workingCompanySettings[this.selectedCompanyId]) {
                this.companiesSettings[this.selectedCompanyId] = {
                    ...this.workingCompanySettings[this.selectedCompanyId]
                };
            }

            this.selectedCompanyId = companyId;
            this.$set(this.workingCompanySettings, companyId, {
                ...this.initialCompaniesSettings[companyId]
            });
            this.dropdownOpen = false;
        },
        handleClickOutside(event) {
            if (!this.$el.contains(event.target)) {
                this.dropdownOpen = false;
            }
        },
        companiesWithInvalidAddress() {
            const companyIds = Object.keys(this.companiesSettings);
            if (companyIds.length > 0) {
                return companyIds.filter(id => this.companiesSettings[id].needValidAddress);
            }
            return [];
        },
        getMailSettings() {
            return this.mailboxConfig[this.selectedCompanySettings.mailboxTier]['post-item'];
        },
        getParcelSettings() {
            return this.mailboxConfig[this.selectedCompanySettings.mailboxTier]['parcel'];
        },
        async saveSettings() {
            this.errorMessage = null;
            this.noChangesMessage = null;

            if (this.selectedCompanyId && this.workingCompanySettings[this.selectedCompanyId]) {
                this.companiesSettings[this.selectedCompanyId] = {
                    ...this.workingCompanySettings[this.selectedCompanyId]
                };
            }

            const customerChanged = JSON.stringify(this.customerSettings) !== JSON.stringify(this.initialCustomerSettings);
            const companyChanged = Object.keys(this.initialCompaniesSettings).length > 0
                ? JSON.stringify(this.companiesSettings[this.selectedCompanyId]) !== JSON.stringify(this.initialCompaniesSettings[this.selectedCompanyId])
                : false;

            if (!customerChanged && !companyChanged) {
                this.noChangesMessage = "* No changes detected to save.";
                return;
            }

            try {
                if (customerChanged) {
                    const customerResponse = await api.post(
                        `/api/mail-forwarding/save-customer-mailbox-settings/${this.customerSettings.customerId}/`,
                        this.customerSettings
                    );

                    if (customerResponse.status !== 200) {
                        throw new Error(customerResponse.data?.error || "Failed to save customer settings.");
                    }
                }

                if (companyChanged) {
                    const companyResponse = await api.post(
                        `/api/mail-forwarding/save-company-mailbox-settings/${this.selectedCompanyId}/`,
                        this.companiesSettings[this.selectedCompanyId]
                    );

                    if (companyResponse.status !== 200) {
                        throw new Error(companyResponse.data?.error || "Failed to save company settings.");
                    }

                    this.initialCompaniesSettings[this.selectedCompanyId] = {
                        ...this.companiesSettings[this.selectedCompanyId]
                    };
                    this.workingCompanySettings[this.selectedCompanyId] = {
                        ...this.companiesSettings[this.selectedCompanyId]
                    };
                }

                if (this.companiesWithInvalidAddress().length <= 1) {
                    window.location.href = '/company-inbox/?success=true';
                    return;
                }
                window.location.href = '/company-inbox/settings/?success=true';
            } catch (error) {
                this.errorMessage = error.response?.data?.error || "Failed to save settings.";
            }
        },
    },
});
</script>

<style scoped>
.fade-label {
    color: rgba(0, 0, 0, 0.5);
}

.fade-label .mailbox-cta-text {
    color: rgba(0, 0, 0, 1);
}

.custom-select {
    position: relative;
    width: 100%;
    max-width: 600px;
    margin-bottom: 15px;
}

.select-header {
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: white;
}

.select-header:hover {
    border-color: #aaa;
}

.arrow {
    transition: transform 0.2s;
    flex-shrink: 0;
    margin-left: 10px;
    font-size: 0.8em;
    color: #666;
    display: inline-flex;
    align-items: center;
    height: 100%;
    transition: transform 0.2s;
}

.arrow.open {
    transform: rotate(180deg);
}

.select-options {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 1000;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: white;
    max-height: 300px;
    overflow-y: auto;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.select-option {
    padding: 10px 15px;
    cursor: pointer;
}

.select-option:hover {
    background-color: #f5f5f5;
}

.select-option.selected {
    background-color: #e0e0e0;
}

.select-option.needs-address {
    background-color: #ffebee;
    color: #d32f2f;
    font-weight: bold;
}

.address-warning {
    font-size: 0.8em;
    margin-left: 10px;
    color: #d32f2f;
    font-weight: normal;
    width: 20%;
}
</style>